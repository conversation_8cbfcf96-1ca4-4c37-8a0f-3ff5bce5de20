﻿#include "m35_ai_adjust.h"
#include "ui_m35_ai_adjust.h"
#include <QDebug>

#include "../io_base_controls/io_ai_adjust/io_ai_adjust.h"
#include "../io_base_controls/io_ai_adjust/io_ai_adjust_button.h"

M35AiAdjust::M35AiAdjust(QWidget* parent)
    : IoBaseControls(parent)
    , ui(new Ui::M35AiAdjust)
{
    ui->setupUi(this);

    init_connect();
}

M35AiAdjust::~M35AiAdjust()
{
    delete ui;
}

//-------------------------------------------------
// 通讯数据相关
// 参考M35项目通讯协议
void M35AiAdjust::parse_data(unsigned char* dat, int len, void* buffer, int buffer_len)
{
    if (current_state_ == GET_AI) {
        // 16通道
        if (ui->tabWidget->currentIndex() == 0) {
            if (dat[0] != 0xCA || dat[1] != 0x01) {
                return;
            }
            ui->WDT_Channel_16->parse_data(dat, len, nullptr, 0);
        }
        // 32通道   M35通讯协议1.9.3版本增加了此功能码
        else if (ui->tabWidget->currentIndex() == 1) {
            if (dat[0] != 0xCA || dat[1] != 0x02) {
                return;
            }
            ui->WDT_Channel_32->parse_data(dat, len, nullptr, 0);
        }
        change_current_state(GET_AI, 1000, true);
    }
}

int M35AiAdjust::pack_data(unsigned char* dat, uint8_t major_id, uint8_t minor_id)
{
    if (current_state_ == GET_AI) {
        int send_length = 48;
        dat[0]          = 0x8A;
        dat[1]          = 0x02;
        // 16通道
        if (ui->tabWidget->currentIndex() == 0) {
            // 功能码
            dat[1] = 0x01;
            // 发送长度
            dat[2] = 0x00;
            dat[3] = 0x30;
            //
            ui->WDT_Channel_16->pack_data(dat, 0, 0);
            // 整体使能位
            ui->CKB_Enable->isChecked() ? dat[12] = 1 : dat[12] = 0;
            //
            send_length = 48;
        }
        // 32通道   M35通讯协议1.9.3版本增加了此功能码
        else if (ui->tabWidget->currentIndex() == 1) {
            //
            dat[1] = 0x02;
            //
            dat[2] = 0x00;
            dat[3] = 0x40;
            //
            ui->WDT_Channel_32->pack_data(dat, 0, 0);
            //
            ui->CKB_Enable->isChecked() ? dat[20] = 1 : dat[20] = 0;
            //
            send_length = 64;
        }
        //
        //
        change_current_state(1000, false);
        return send_length;
    }
    return 0;
}

int M35AiAdjust::after_state_changed()
{
    change_current_state(GET_AI, 1000, true);
    //
    IoEasyLog easylog(project_);
    easylog.log_info("当前模块: AD校准");
}

void M35AiAdjust::init_connect()
{
    // 一键校准
    connect(ui->PB_OneKey, &QPushButton::clicked, this, [=]() {
        if (ui->tabWidget->currentIndex() == 0) {
            QList<IoAiAdjustButton*> buttons = ui->WDT_Channel_16->findChildren<IoAiAdjustButton*>();
            for (IoAiAdjustButton* button : buttons) {
                button->set_enable(true);
            }
        }
        else if (ui->tabWidget->currentIndex() == 1) {
            QList<IoAiAdjustButton*> buttons = ui->WDT_Channel_32->findChildren<IoAiAdjustButton*>();
            for (IoAiAdjustButton* button : buttons) {
                button->set_enable(true);
            }
        }
    });

    // 可用
    connect(ui->CKB_Enable, &QCheckBox::stateChanged, this, [=](int state) {
        if (state == Qt::Checked) {
            ui->tabWidget->setEnabled(true);
        }
        else {
            ui->tabWidget->setEnabled(false);
        }
    });

    // 全选组1
    connect(ui->CKB_Group1, &QCheckBox::stateChanged, this, [=](int state) {
        if (ui->CKB_Enable->isChecked() == false) {
            return;
        }
        if (ui->tabWidget->currentIndex() == 0) {
            QList<IoAiAdjust*> ai_list = ui->WDT_Channel_16->findChildren<IoAiAdjust*>();
            for (IoAiAdjust* ai : ai_list) {
                if (ai->get_widget_column() == 0) {
                    ai->set_mask_checked(state);
                }
            }
        }
        else if (ui->tabWidget->currentIndex() == 1) {
            QList<IoAiAdjust*> ai_list = ui->WDT_Channel_32->findChildren<IoAiAdjust*>();
            for (IoAiAdjust* ai : ai_list) {
                if (ai->get_widget_column() == 0) {
                    ai->set_mask_checked(state);
                }
            }
        }
    });

    // 全选组2
    connect(ui->CKB_Group2, &QCheckBox::stateChanged, this, [=](int state) {
        if (ui->CKB_Enable->isChecked() == false) {
            return;
        }
        if (ui->tabWidget->currentIndex() == 0) {
            QList<IoAiAdjust*> ai_list = ui->WDT_Channel_16->findChildren<IoAiAdjust*>();
            for (IoAiAdjust* ai : ai_list) {
                if (ai->get_widget_column() == 1) {
                    ai->set_mask_checked(state);
                }
            }
        }
        else if (ui->tabWidget->currentIndex() == 1) {
            QList<IoAiAdjust*> ai_list = ui->WDT_Channel_32->findChildren<IoAiAdjust*>();
            for (IoAiAdjust* ai : ai_list) {
                if (ai->get_widget_column() == 1) {
                    ai->set_mask_checked(state);
                }
            }
        }
    });

    // 全选组2
    connect(ui->CKB_Group3, &QCheckBox::stateChanged, this, [=](int state) {
        if (ui->CKB_Enable->isChecked() == false) {
            return;
        }
        if (ui->tabWidget->currentIndex() == 0) {
            QList<IoAiAdjust*> ai_list = ui->WDT_Channel_16->findChildren<IoAiAdjust*>();
            for (IoAiAdjust* ai : ai_list) {
                if (ai->get_widget_column() == 2) {
                    ai->set_mask_checked(state);
                }
            }
        }
        else if (ui->tabWidget->currentIndex() == 1) {
            QList<IoAiAdjust*> ai_list = ui->WDT_Channel_32->findChildren<IoAiAdjust*>();
            for (IoAiAdjust* ai : ai_list) {
                if (ai->get_widget_column() == 2) {
                    ai->set_mask_checked(state);
                }
            }
        }
    });
}
//-------------------------------------------------
// 初始化相关
void M35AiAdjust::init(QString project)
{
    project_      = project;
    ai_adjust_16_ = QString("./projects/%1/xml/m35_ai_adjust_16.xml").arg(project_);
    ai_adjust_32_ = QString("./projects/%1/xml/m35_ai_adjust_32.xml").arg(project_);
    ui->WDT_Channel_16->init(project_, ai_adjust_16_);
    ui->WDT_Channel_32->init(project_, ai_adjust_32_);
    //
    ui->tabWidget->setEnabled(false);
    change_current_state(GET_AI, 1000, true);
}

void M35AiAdjust::set_checked(bool state)
{
    ui->CKB_Enable->setChecked(state);
}
