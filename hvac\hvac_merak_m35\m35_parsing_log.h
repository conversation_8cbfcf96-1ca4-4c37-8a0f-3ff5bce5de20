#ifndef M35_PARSING_LOG_H
#define M35_PARSING_LOG_H

#include "hvac_global.h"
#include <QWidget>

#include "../io_easy_log/io_easy_log.h"
#include "../io_xml_parser/io_xml_parser.h"
#include "../io_file_parser/io_file_parser.h"
#include "../io_base_controls/io_base_controls.h"
#include "../io_custom_component/io_parsing_logs_widget/io_parsing_logs_widget.h"

namespace Ui {
class M35ParsingLog;
}

class HVACSHARED_EXPORT M35ParsingLog : public IoBaseControls
{
    Q_OBJECT

public:
    explicit M35ParsingLog(QWidget *parent = 0);
    ~M35ParsingLog();

private:
    Ui::M35ParsingLog *ui;

public:
    void init(QString project);
    void load_log_xml(QString dir);

    int after_state_changed();

private:
    QString project_;
    QString xml_log_dir_;

    IoXmlParser xml_parser_;

};

#endif // KTX_PARSING_LOG_H
