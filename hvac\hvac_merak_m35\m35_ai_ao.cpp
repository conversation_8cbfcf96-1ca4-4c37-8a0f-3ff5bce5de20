﻿#include "m35_ai_ao.h"
#include "ui_m35_ai_ao.h"

// 不同板卡使用的功能码也不同, 请查看通讯协议文件
static uint8_t g_command_id = 0;

M35AiAo::M35AiAo(QWidget *parent) : IoBaseControls(parent),
    ui(new Ui::M35AiAo)
{
    ui->setupUi(this);
}

M35AiAo::~M35AiAo()
{
    delete ui;
}

//-------------------------------------------------
// 参考M35项目通讯协议
void M35AiAo::parse_data(unsigned char *dat, int len, void *buffer, int buffer_len)
{
    if(current_state_ == GET_AI_AO){
        if(dat[0] != 0xC3 || dat[1] != g_command_id){
            return;
        }
        //
        IoBaseForm* current_page = static_cast<IoBaseForm*>(ui->tabWidget->currentWidget());
        if(current_page != nullptr){
            current_page->parse_data(dat,len,nullptr,0);
        }
        //
        change_current_state(GET_AI_AO,1000,true);
    }
}

int M35AiAo::pack_data(unsigned char *dat,uint8_t major_id,uint8_t minor_id)
{
    if(current_state_ == GET_AI_AO){
        // page的顺序 与 通讯协议中功能码的顺序 对应
        g_command_id = static_cast<uint8_t>(ui->tabWidget->currentIndex()) + 1;
        //
        dat[0] = 0x83;
        dat[1] = g_command_id;
        dat[2] = 0x00;
        dat[3] = 0x30;
        //
        IoBaseForm* current_page = static_cast<IoBaseForm*>(ui->tabWidget->currentWidget());
        if(current_page != nullptr){
            current_page->pack_data(dat,major_id,minor_id);
        }
        //
        change_current_state(1000,false);
        //
        int send_length = 48;
        return send_length;
    }

    return 0;
}
//-------------------------------------------------
// 初始化相关
void M35AiAo::init(QString project)
{
    project_ = project;
    //
    aio_control_xml_ = QString("./projects/%1/xml/m35_aio_control.xml").arg(project_);
    aio_driver_xml_ = QString("./projects/%1/xml/m35_aio_driver.xml").arg(project_);
    aio_extend_xml_ = QString("./projects/%1/xml/m35_aio_extend.xml").arg(project_);
    aio_bgd_xml_ = QString("./projects/%1/xml/m35_aio_bgd.xml").arg(project_);
    aio_exio_xml_ = QString("./projects/%1/xml/m35_aio_exio.xml").arg(project_);
    //
    ui->WDT_Control->init(project_,aio_control_xml_);
    ui->WDT_Driver->init(project_,aio_driver_xml_);
    ui->WDT_Extend->init(project_,aio_extend_xml_);
    ui->WDT_Bgd->init(project_,aio_bgd_xml_);
    ui->WDT_Exio->init(project_,aio_exio_xml_);
    //
    change_current_state(GET_AI_AO,1000,true);
}

//-------------------------------------------------
// 切换模块相关
int M35AiAo::after_state_changed()
{
    change_current_state(GET_AI_AO,1000,true);

    IoEasyLog easylog(project_);
    easylog.log_info("当前模块: HAVC-M35-AIAO");
}
