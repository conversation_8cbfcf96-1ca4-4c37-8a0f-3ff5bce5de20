<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>M35AiAdjust</class>
 <widget class="QWidget" name="M35AiAdjust">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1155</width>
    <height>579</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QPushButton
{
	border: 1px solid #7f8585; 	
	border-radius: 2px; 		
	background-color: rgb(225, 225, 225);
	font-size: 14px;
	min-height: 26px;
}

QPushButton:hover 
{
	background-color: #4b6e8a; 
	color:#ffffff;
}

QPushButton:pressed 
{
	background-color: #4b6e8a; 
	color:#ffffff;
}

</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <property name="leftMargin">
    <number>2</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <property name="spacing">
    <number>2</number>
   </property>
   <item row="0" column="0">
    <widget class="QGroupBox" name="GB_Control">
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>2</number>
      </property>
      <property name="topMargin">
       <number>2</number>
      </property>
      <property name="bottomMargin">
       <number>2</number>
      </property>
      <property name="spacing">
       <number>2</number>
      </property>
      <item row="0" column="2">
       <widget class="QPushButton" name="PB_OneKey">
        <property name="minimumSize">
         <size>
          <width>116</width>
          <height>28</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>116</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>一键校准</string>
        </property>
       </widget>
      </item>
      <item row="0" column="5">
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>17</width>
          <height>17</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="3">
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Minimum</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="4">
       <widget class="QCheckBox" name="CKB_Enable">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="autoFillBackground">
         <bool>false</bool>
        </property>
        <property name="text">
         <string>可用</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QGroupBox" name="groupBox">
        <property name="title">
         <string/>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="QCheckBox" name="CKB_Group1">
           <property name="minimumSize">
            <size>
             <width>220</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>220</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="layoutDirection">
            <enum>Qt::RightToLeft</enum>
           </property>
           <property name="text">
            <string>全选</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QCheckBox" name="CKB_Group2">
           <property name="minimumSize">
            <size>
             <width>250</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>250</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="layoutDirection">
            <enum>Qt::RightToLeft</enum>
           </property>
           <property name="text">
            <string>全选</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QCheckBox" name="CKB_Group3">
           <property name="minimumSize">
            <size>
             <width>245</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>245</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="layoutDirection">
            <enum>Qt::RightToLeft</enum>
           </property>
           <property name="text">
            <string>全选</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="TAB_Channel_16">
      <attribute name="title">
       <string>16通道</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <property name="leftMargin">
        <number>1</number>
       </property>
       <property name="topMargin">
        <number>1</number>
       </property>
       <property name="rightMargin">
        <number>1</number>
       </property>
       <property name="bottomMargin">
        <number>1</number>
       </property>
       <property name="spacing">
        <number>1</number>
       </property>
       <item row="0" column="0">
        <widget class="IoBaseForm" name="WDT_Channel_16" native="true"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="TAB_Channel_32">
      <attribute name="title">
       <string>32通道</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_5">
       <property name="leftMargin">
        <number>1</number>
       </property>
       <property name="topMargin">
        <number>1</number>
       </property>
       <property name="rightMargin">
        <number>1</number>
       </property>
       <property name="bottomMargin">
        <number>1</number>
       </property>
       <property name="spacing">
        <number>1</number>
       </property>
       <item row="0" column="0">
        <widget class="IoBaseForm" name="WDT_Channel_32" native="true"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>IoBaseForm</class>
   <extends>QWidget</extends>
   <header>../io_custom_component/io_base_form/io_base_form.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
