﻿#include "m35_device_info2.h"
#include "ui_m35_device_info2.h"

#include <QHostAddress>
#include <QtDebug>
#include <QtEndian>
#include <time.h>

M35DeviceInfo2::M35DeviceInfo2(QWidget *parent) : IoBaseControls(parent),
    ui(new Ui::M35DeviceInfo2)
{
    ui->setupUi(this);

    init_widget();
    init_connect();
}

M35DeviceInfo2::~M35DeviceInfo2()
{
    delete ui;
}

//-------------------------------------------------
// 通讯数据相关
void M35DeviceInfo2::parse_data(unsigned char *dat, int len, void *buffer, int buffer_len)
{
    if(current_state_ == GET_DEVICE_INFO1){
        if(dat[0] != 0xC0 || dat[1] != 01){
            return;
        }
        parse_device_info1(dat,len);
        change_current_state(GET_DEVICE_INFO2,1000,true);
    }
    else if(current_state_ == GET_DEVICE_INFO2){
        if(dat[0] != 0xC0 || dat[1] != 02){
            return;
        }
        parse_device_info2(dat,len);
        change_current_state(GET_DEVICE_INFO1,2000,true);
    }
    else if(current_state_ == SET_NODE_ID){
        device_info2_request_.node_set = 0;         // 清除使能
        change_current_state(GET_DEVICE_INFO1,1000,true);
    }
    else if(current_state_ == SET_K1_RTC){
        device_info2_request_.k1_rtc_syn = 0;       // 清除使能
        change_current_state(GET_DEVICE_INFO1,1000,true);
        return;
    }
    else if(current_state_ == SET_K20_RTC){
        device_info2_request_.k20_rtc_syn = 0;      // 清除使能
        change_current_state(GET_DEVICE_INFO1,1000,true);
        return;
    }
    else if(current_state_ == STOP){
        change_current_state(STOP,100,true);
        return;
    }
}

int M35DeviceInfo2::pack_data(unsigned char *dat,uint8_t major_id,uint8_t minor_id)
{
    if(current_state_ == GET_DEVICE_INFO1){
        uint16_t send_length = pack_device_info1(dat);
        change_current_state(2000,false);
        return send_length;
    }
    else if(current_state_ == GET_DEVICE_INFO2){
        uint16_t send_length = pack_device_info2(dat);
        change_current_state(2000,false);
        return send_length;
    }
    else if(current_state_ == SET_NODE_ID){
        uint16_t send_length = pack_set_id(dat);
        change_current_state(1000,false);
        return send_length;
    }
    else if(current_state_ == SET_K1_RTC){
        uint16_t send_length = pack_set_k1_rtc(dat);
        change_current_state(1000,false);
        return send_length;
    }
    else if(current_state_ == SET_K20_RTC){
        uint16_t send_length = pack_set_k20_rtc(dat);
        change_current_state(1000,false);
        return send_length;
    }
    else if(current_state_ == STOP){
        change_current_state(STOP,100,true);
        return 0;
    }
}

int M35DeviceInfo2::after_state_changed()
{
    change_current_state(GET_DEVICE_INFO1,1000,true);

    IoEasyLog easylog(project_);
    easylog.log_info("当前模块: HAVC-M35-板卡配置");
}

void M35DeviceInfo2::init_widget()
{
    // 正则表达式, 只允许输入正整数
    QRegularExpression regex("0|[1-9][0-9]*");
    QRegularExpressionValidator *validator = new QRegularExpressionValidator(regex);
    ui->LE_Node->setValidator(validator);

    //
    timer_.setTimerType(Qt::PreciseTimer);
    timer_.setInterval(1000);
}

//-------------------------------------------------
// 初始化相关
void M35DeviceInfo2::init(QString project)
{
    project_ = project;
    change_current_state(GET_DEVICE_INFO1,1000,true);
}

void M35DeviceInfo2::init_connect()
{
    // 防止ID设置为空
    connect(ui->LE_Node,&QLineEdit::textChanged,this,[=](QString text){
        if(text.isEmpty()){
            ui->LE_Node->setText("0");
        }
    });

    // 设置板卡ID
    connect(ui->PB_Set_Node,&QPushButton::clicked,this,[=](){
        change_next_state(SET_NODE_ID,1000,true);
    });

    // 同步K1-RTC
    connect(ui->PB_K1_Rtc,&QPushButton::clicked,this,[=](){
        change_next_state(SET_K1_RTC,1000,true);
    });

    // 同步K20-RTC
    connect(ui->PB_K20_Rtc,&QPushButton::clicked,this,[=](){
        change_next_state(SET_K20_RTC,1000,true);
    });

    // 断开1秒后显示离线
    connect(&timer_,&QTimer::timeout,this,[=](){
        ui->LB_K1_State->setStyleSheet(LED_WHITE);
        ui->LB_K2_State->setStyleSheet(LED_WHITE);
        ui->LB_K3_State->setStyleSheet(LED_WHITE);
        ui->LB_K4_State->setStyleSheet(LED_WHITE);
        ui->LB_K20_State->setStyleSheet(LED_WHITE);
        ui->LB_K21_State->setStyleSheet(LED_WHITE);
        //
        ui->LB_K1_Nor->setStyleSheet(LED_WHITE);
        ui->LB_K2_Nor->setStyleSheet(LED_WHITE);
        ui->LB_K3_Nor->setStyleSheet(LED_WHITE);
        ui->LB_K4_Nor->setStyleSheet(LED_WHITE);
        ui->LB_K20_Nor->setStyleSheet(LED_WHITE);
        ui->LB_K21_Nor->setStyleSheet(LED_WHITE);
        //
        ui->LB_K1_Nand->setStyleSheet(LED_WHITE);
        //
        ui->LB_K1_Hse->setStyleSheet(LED_WHITE);
        ui->LB_K2_Hse->setStyleSheet(LED_WHITE);
        ui->LB_K3_Hse->setStyleSheet(LED_WHITE);
        ui->LB_K4_Hse->setStyleSheet(LED_WHITE);
        ui->LB_K20_Hse->setStyleSheet(LED_WHITE);
        ui->LB_K21_Hse->setStyleSheet(LED_WHITE);
        //
        timer_.stop();

    });

}

// 网络状态 请求
int M35DeviceInfo2::pack_device_info1(unsigned char *dat)
{
    uint16_t send_length = sizeof(DEVICE_INFO1_REQUEST);
    memset(&device_info1_request_,0,sizeof(DEVICE_INFO1_REQUEST));

    device_info1_request_.head = 0x80;
    device_info1_request_.command = 0x01;
    device_info1_request_.size = qToBigEndian(send_length); // 大端
    //
    memcpy(dat,&device_info1_request_,send_length);
    return send_length;
}


// 设备信息 请求
int M35DeviceInfo2::pack_device_info2(unsigned char *dat)
{
    uint16_t send_length = sizeof(DEVICE_INFO2_REQUEST);
    memset(&device_info2_request_,0,sizeof(DEVICE_INFO2_REQUEST));
    //
    device_info2_request_.head = 0x80;
    device_info2_request_.command = 0x02;
    device_info2_request_.size = qToBigEndian(send_length); // 大端
    device_info2_request_.local_rtc = time(nullptr);
    //
    memcpy(dat,&device_info2_request_,send_length);
    return send_length;
}

// 设置板卡ID    一般是EXIO板卡ID
int M35DeviceInfo2::pack_set_id(unsigned char *dat)
{
    uint16_t send_length = sizeof(DEVICE_INFO2_REQUEST);
    memset(&device_info2_request_,0,sizeof(DEVICE_INFO2_REQUEST));
    //
    device_info2_request_.head = 0x80;
    device_info2_request_.command = 0x02;
    device_info2_request_.size = qToBigEndian(send_length); // 大端
    device_info2_request_.local_rtc = time(nullptr);
    //
    uint8_t id = (uint8_t)ui->LE_Node->text().toInt();
    device_info2_request_.node = id;
    device_info2_request_.node_set = 1; // 使能
    //
    memcpy(dat,&device_info2_request_,send_length);

    // 记录操作日志
    IoEasyLog easylog(project_);
    QString log_rtc = QString(tr("设置EXIO板卡ID:%1")).arg(id);
    easylog.log_info(log_rtc);


    return send_length;
}

//
int M35DeviceInfo2::pack_set_k1_rtc(unsigned char *dat)
{
    uint16_t send_length = sizeof(DEVICE_INFO2_REQUEST);
    memset(&device_info2_request_,0,sizeof(DEVICE_INFO2_REQUEST));
    //
    device_info2_request_.head = 0x80;
    device_info2_request_.command = 0x02;
    device_info2_request_.size = qToBigEndian(send_length); // 大端
    // 同步RTC
    device_info2_request_.local_rtc = time(nullptr);
    device_info2_request_.k1_rtc_syn = 1;
    //
    memcpy(dat,&device_info2_request_,send_length);
    // 记录操作日志
    IoEasyLog easylog(project_);
    QString log_rtc = QString(tr("K1同步RTC:%1")).arg(IoDateTime::get_datetime_string2());
    easylog.log_info(log_rtc);

    return send_length;
}

int M35DeviceInfo2::pack_set_k20_rtc(unsigned char *dat)
{
    uint16_t send_length = sizeof(DEVICE_INFO2_REQUEST);
    memset(&device_info2_request_,0,sizeof(DEVICE_INFO2_REQUEST));
    //
    device_info2_request_.head = 0x80;
    device_info2_request_.command = 0x02;
    device_info2_request_.size = qToBigEndian(send_length); // 大端
    // 同步RTC
    device_info2_request_.local_rtc = time(nullptr);
    device_info2_request_.k20_rtc_syn = 1;
    //
    memcpy(dat,&device_info2_request_,send_length);
    // 记录操作日志
    IoEasyLog easylog(project_);
    QString log_rtc = QString(tr("K20同步RTC:%1")).arg(IoDateTime::get_datetime_string2());
    easylog.log_info(log_rtc);

    return send_length;
}

// 网络状态 回复
void M35DeviceInfo2::parse_device_info1(unsigned char *dat, int len)
{
    memcpy(&device_info1_response_,dat,sizeof(DEVICE_INFO1_RESPONSE));
    //
    parse_exio1_soft();
    parse_exio2_soft();
}

// 设备信息 回复
void M35DeviceInfo2::parse_device_info2(unsigned char *dat, int len)
{
    memcpy(&device_info2_response_,dat,sizeof(DEVICE_INFO2_RESPONSE));
    //
    parse_k1_state();
    parse_k2_state();
    parse_k3_state();
    parse_k4_state();
    parse_k20_state();
    parse_k21_state();
    //
    parse_k1_boot();
    parse_k2_boot();
    parse_k3_boot();
    parse_k4_boot();
    parse_k20_boot();
    parse_k21_boot();
    parse_exio1_boot();
    parse_exio2_boot();
    parse_node();

    // 保持通讯灯正常
    timer_.start();
}

void M35DeviceInfo2::parse_k1_state()
{
    QString k1_id,k1_5v,k1_rtc,k1_ip;
    //
    if(device_info2_response_.k1_id == 0){
        ui->LB_K1_State->setStyleSheet(LED_WHITE);
        return;
    }
    // 状态
    ui->LB_K1_State->setStyleSheet(LED_GREEN);
    device_info2_response_.k1_nor ? ui->LB_K1_Nor->setStyleSheet(LED_GREEN) : ui->LB_K1_Nor->setStyleSheet(LED_WHITE);
    device_info2_response_.k1_nand ? ui->LB_K1_Nand->setStyleSheet(LED_GREEN) : ui->LB_K1_Nand->setStyleSheet(LED_WHITE);
    device_info2_response_.k1_hse ? ui->LB_K1_Hse->setStyleSheet(LED_GREEN) : ui->LB_K1_Hse->setStyleSheet(LED_WHITE);
    // 参数
    k1_id.sprintf("%X",device_info2_response_.k1_id);
    k1_5v.sprintf("%d.%dV",device_info2_response_.k1_5v / 10,device_info2_response_.k1_5v % 10);
    k1_rtc = QDateTime::fromTime_t(device_info2_response_.k1_rtc).toLocalTime().toString("yyyy-MM-dd hh:mm:ss");
    k1_ip = QHostAddress(device_info2_response_.k1_ipv4).toString();

    ui->LE_K1_Id->setText(k1_id);
    ui->LE_K1_5v->setText(k1_5v);
    ui->LE_K1_Rtc->setText(k1_rtc);
    ui->LE_K1_Ip->setText(k1_ip);
}

void M35DeviceInfo2::parse_k2_state()
{
    QString k2_id,k2_5v;
    //
    if(device_info2_response_.k2_id == 0){
        ui->LB_K2_State->setStyleSheet(LED_WHITE);
        return;
    }
    // 状态
    ui->LB_K2_State->setStyleSheet(LED_GREEN);
    device_info2_response_.k2_nor ? ui->LB_K2_Nor->setStyleSheet(LED_GREEN) : ui->LB_K2_Nor->setStyleSheet(LED_WHITE);
    device_info2_response_.k2_hse ? ui->LB_K2_Hse->setStyleSheet(LED_GREEN) : ui->LB_K2_Hse->setStyleSheet(LED_WHITE);
    // 参数
    k2_id.sprintf("%X",device_info2_response_.k2_id);
    k2_5v.sprintf("%d.%dV",device_info2_response_.k2_5v / 10,device_info2_response_.k2_5v % 10);

    ui->LE_K2_Id->setText(k2_id);
    ui->LE_K2_5v->setText(k2_5v);
}

void M35DeviceInfo2::parse_k3_state()
{
    QString k3_id,k3_5v;
    //
    if(device_info2_response_.k3_id == 0){
        ui->LB_K3_State->setStyleSheet(LED_WHITE);
        return;
    }
    // 状态
    ui->LB_K3_State->setStyleSheet(LED_GREEN);
    device_info2_response_.k3_nor ? ui->LB_K3_Nor->setStyleSheet(LED_GREEN) : ui->LB_K3_Nor->setStyleSheet(LED_WHITE);
    device_info2_response_.k3_hse ? ui->LB_K3_Hse->setStyleSheet(LED_GREEN) : ui->LB_K3_Hse->setStyleSheet(LED_WHITE);
    // 参数
    k3_id.sprintf("%X",device_info2_response_.k3_id);
    k3_5v.sprintf("%d.%dV",device_info2_response_.k3_5v / 10,device_info2_response_.k3_5v % 10);

    ui->LE_K3_Id->setText(k3_id);
    ui->LE_K3_5v->setText(k3_5v);
}

void M35DeviceInfo2::parse_k4_state()
{
    QString k4_id,k4_5v;
    //
    if(device_info2_response_.k4_id == 0){
        ui->LB_K4_State->setStyleSheet(LED_WHITE);
        return;
    }
    // 状态
    ui->LB_K4_State->setStyleSheet(LED_GREEN);
    device_info2_response_.k4_nor ? ui->LB_K4_Nor->setStyleSheet(LED_GREEN) : ui->LB_K4_Nor->setStyleSheet(LED_WHITE);
    device_info2_response_.k4_hse ? ui->LB_K4_Hse->setStyleSheet(LED_GREEN) : ui->LB_K4_Hse->setStyleSheet(LED_WHITE);
    // 参数
    k4_id.sprintf("%X",device_info2_response_.k4_id);
    k4_5v.sprintf("%d.%dV",device_info2_response_.k4_5v / 10,device_info2_response_.k4_5v % 10);

    ui->LE_K4_Id->setText(k4_id);
    ui->LE_K4_5v->setText(k4_5v);
}

void M35DeviceInfo2::parse_k20_state()
{
    QString k20_id,k20_5v,k20_rtc;
    //
    if(device_info2_response_.k20_id == 0){
        ui->LB_K20_State->setStyleSheet(LED_WHITE);
        return;
    }
    // 状态
    ui->LB_K20_State->setStyleSheet(LED_GREEN);
    device_info2_response_.k20_nor ? ui->LB_K20_Nor->setStyleSheet(LED_GREEN) : ui->LB_K20_Nor->setStyleSheet(LED_WHITE);
    device_info2_response_.k20_hse ? ui->LB_K20_Hse->setStyleSheet(LED_GREEN) : ui->LB_K20_Hse->setStyleSheet(LED_WHITE);
    // 参数
    k20_id.sprintf("%X",device_info2_response_.k20_id);
    k20_5v.sprintf("%d.%dV",device_info2_response_.k20_5v / 10,device_info2_response_.k20_5v % 10);
    k20_rtc = QDateTime::fromTime_t(device_info2_response_.k20_rtc).toLocalTime().toString("yyyy-MM-dd hh:mm:ss");

    ui->LE_K20_Id->setText(k20_id);
    ui->LE_K20_5v->setText(k20_5v);
    ui->LE_K20_Rtc->setText(k20_rtc);
}

void M35DeviceInfo2::parse_k21_state()
{
    QString k21_id,k21_5v;
    //
    if(device_info2_response_.k21_id == 0){
        ui->LB_K21_State->setStyleSheet(LED_WHITE);
        return;
    }
    // 状态
    ui->LB_K21_State->setStyleSheet(LED_GREEN);
    device_info2_response_.k21_nor ? ui->LB_K21_Nor->setStyleSheet(LED_GREEN) : ui->LB_K21_Nor->setStyleSheet(LED_WHITE);
    device_info2_response_.k21_hse ? ui->LB_K21_Hse->setStyleSheet(LED_GREEN) : ui->LB_K21_Hse->setStyleSheet(LED_WHITE);
    // 参数
    k21_id.sprintf("%X",device_info2_response_.k21_id);
    k21_5v.sprintf("%d.%dV",device_info2_response_.k21_5v / 10,device_info2_response_.k21_5v % 10);

    ui->LE_K21_Id->setText(k21_id);
    ui->LE_K21_5v->setText(k21_5v);
}

void M35DeviceInfo2::parse_k1_boot()
{
    QString k1_boot,k1_cpld,k1_mvb_soft;

    k1_boot.sprintf("%X.%02X",device_info2_response_.k1_boot_ver >> 8,device_info2_response_.k1_boot_ver & 0xFF);
    k1_cpld.sprintf("%X.%02X",device_info2_response_.k1_cpld_ver >> 8,device_info2_response_.k1_cpld_ver & 0xFF);
    k1_mvb_soft.sprintf("%X.%X",(device_info2_response_.mvb_soft_ver & 0xF0) >> 4, device_info2_response_.mvb_soft_ver & 0xF);  // 2022-04-11 高字节未使用

    ui->LE_K1_Boot->setText(k1_boot);
    ui->LE_K1_Cpld->setText(k1_cpld);
    ui->LE_K1_Mvb->setText(k1_mvb_soft);
}

void M35DeviceInfo2::parse_k2_boot()
{
    QString k2_boot,k2_cpld;

    k2_boot.sprintf("%02X.%02X",device_info2_response_.k2_boot_ver >> 8,device_info2_response_.k2_boot_ver & 0xFF);
    k2_cpld.sprintf("%X.%02X",device_info2_response_.k2_cpld_ver >> 8,device_info2_response_.k2_cpld_ver & 0xFF);

    ui->LE_K2_Boot->setText(k2_boot);
    ui->LE_K2_Cpld->setText(k2_cpld);
}

void M35DeviceInfo2::parse_k3_boot()
{
    QString k3_boot,k3_cpld;

    k3_boot.sprintf("%02X.%02X",device_info2_response_.k3_boot_ver >> 8,device_info2_response_.k3_boot_ver & 0xFF);
    k3_cpld.sprintf("%X.%02X",device_info2_response_.k3_cpld_ver >> 8,device_info2_response_.k3_cpld_ver & 0xFF);

    ui->LE_K3_Boot->setText(k3_boot);
    ui->LE_K3_Cpld->setText(k3_cpld);
}

void M35DeviceInfo2::parse_k4_boot()
{
    QString k4_boot,k4_cpld;

    k4_boot.sprintf("%02X.%02X",device_info2_response_.k4_boot_ver >> 8,device_info2_response_.k4_boot_ver & 0xFF);
    k4_cpld.sprintf("%X.%02X",device_info2_response_.k4_cpld_ver >> 8,device_info2_response_.k4_cpld_ver & 0xFF);

    ui->LE_K4_Boot->setText(k4_boot);
    ui->LE_K4_Cpld->setText(k4_cpld);
}

void M35DeviceInfo2::parse_k20_boot()
{
    QString k20_boot,k20_cpld;

    k20_boot.sprintf("%02X.%02X",device_info2_response_.k20_boot_ver >> 8,device_info2_response_.k20_boot_ver & 0xFF);
    k20_cpld.sprintf("%X.%02X",device_info2_response_.k20_cpld_ver >> 8,device_info2_response_.k20_cpld_ver & 0xFF);

    ui->LE_K20_Boot->setText(k20_boot);
    ui->LE_K20_Cpld->setText(k20_cpld);
}

void M35DeviceInfo2::parse_k21_boot()
{
    QString k21_boot,k21_cpld;

    k21_boot.sprintf("%02X.%02X",device_info2_response_.k21_boot_ver >> 8,device_info2_response_.k21_boot_ver & 0xFF);
    k21_cpld.sprintf("%X.%02X",device_info2_response_.k21_cpld_ver >> 8,device_info2_response_.k21_cpld_ver & 0xFF);

    ui->LE_K21_Boot->setText(k21_boot);
    ui->LE_K21_Cpld->setText(k21_cpld);
}

void M35DeviceInfo2::parse_exio1_boot()
{
    QString exio1_boot;
    exio1_boot.sprintf("%02X.%02X",device_info2_response_.exio1_boot_ver >> 8,device_info2_response_.exio1_boot_ver & 0xFF);
    ui->LE_Exio01_Boot->setText(exio1_boot);
}

void M35DeviceInfo2::parse_exio2_boot()
{
    QString exio2_boot;
    exio2_boot.sprintf("%02X.%02X",device_info2_response_.exio2_boot_ver >> 8,device_info2_response_.exio2_boot_ver & 0xFF);
    ui->LE_Exio02_Boot->setText(exio2_boot);
}

void M35DeviceInfo2::parse_node()
{
    QString node;
    node.sprintf("%X",device_info2_response_.node);
    ui->LB_Current_Id->setText(node);
}

void M35DeviceInfo2::parse_exio1_soft()
{
    QString exio1_soft;
    exio1_soft.sprintf("%X.%02X",(device_info1_response_.exio1_soft_ver >> 8) & 0x0F,device_info1_response_.exio1_soft_ver & 0xFF);
    ui->LE_Exio01_Soft->setText(exio1_soft);
}

void M35DeviceInfo2::parse_exio2_soft()
{
    QString exio2_soft;
    exio2_soft.sprintf("%X.%02X",(device_info1_response_.exio2_soft_ver >> 8) & 0x0F,device_info1_response_.exio2_soft_ver & 0xFF);
    ui->LE_Exio02_Soft->setText(exio2_soft);
}
