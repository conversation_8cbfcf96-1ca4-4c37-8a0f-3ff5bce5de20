#include "m35_update.h"
#include "ui_m35_update.h"

#include <QDebug>
#include <QMessageBox>
#include <time.h>

#include "../io_convert/io_convert.h"
#include "../io_datetime/io_datetime.h"
#include "../io_dialog/io_dialog.h"
#include "../io_script_engine/io_script_engine.h"

// 各子板ID
#define DEVICE_K1 0x1
#define DEVICE_K2 0x2
#define DEVICE_K3 0x3
#define DEVICE_K4 0x4
#define DEVICE_EXIO 0x5
#define DEVICE_CBM02 0x6
#define DEVICE_MPB01 0x7
#define DEVICE_K20 0x20
#define DEVICE_K21 0x21

M35Update::M35Update(QWidget* parent)
    : IoBaseControls(parent)
    , ui(new Ui::M35Update)
{
    ui->setupUi(this);
}

M35Update::~M35Update()
{
    delete ui;
}

void M35Update::parse_data(unsigned char* dat, int len, void* buffer, int buffer_len)
{
    // 不太懂用义,照抄过来
    static uint8_t update_end = 0;
    static uint8_t update_dev = 0;
    static time_t  start      = 0;
    static time_t  end        = 0;

    // 校验帧头
    if (dat[0] != 0xC6 || dat[1] != 0x01) {
        return;
    }

    // 检测上传完成信号
    if ((dat[4] & 0xFE) != 0) {
        update_end = 1;
        start = end = time(nullptr);
        upload_complete_time_ = time(nullptr);
        update_upload_completed_ = true;
        ui->WDT_Ftp->show_log(tr("文件上传完成，开始发送到子板..."), tr(""), QColor("blue"));
    }
    //
    if (current_state_ == LAUNCH_FTP_SERVICES) {
        // 检查状态
        int ftp_state = IoDataParser::get_bit(dat, 4, 0);
        if (ftp_state == 1) {
            ui->WDT_Ftp->show_log(tr("Ftp服务已启动"), tr(""), QColor("black"));
        }
        // 向Ftp子窗口发射信号,通知可以连接了
        emit sig_ftp_started();
        //
        change_current_state(HEARTBEAT, 1000, true);
    }
    else if (current_state_ == RESTART_MCU) {
        change_current_state(HEARTBEAT, 1000, true);
    }
    else if (current_state_ == UPDATE_BIN) {

        if (update_dev != dat[5]) {
            update_dev = (uint8_t)dat[5];
            //
            if (update_dev == DEVICE_K1) {
                refresh_timer_.start(5000);
                ui->WDT_Ftp->show_log(tr("K1板 更新中..."), tr(""), QColor("red"));
            }
            else if (update_dev == DEVICE_K2) {
                ui->WDT_Ftp->show_log(tr("文件正在发送至K2板中..."), tr(""), QColor("red"));
            }
            else if (update_dev == DEVICE_K3) {
                ui->WDT_Ftp->show_log(tr("文件正在发送至K3板中..."), tr(""), QColor("red"));
            }
            else if (update_dev == DEVICE_K4) {
                ui->WDT_Ftp->show_log(tr("文件正在发送至K4板中..."), tr(""), QColor("red"));
            }
            else if (update_dev == DEVICE_K20) {
                ui->WDT_Ftp->show_log(tr("文件正在发送至K20板中..."), tr(""), QColor("red"));
            }
            else if (update_dev == DEVICE_K21) {
                ui->WDT_Ftp->show_log(tr("文件正在发送至K21板中..."), tr(""), QColor("red"));
            }
            else if (update_dev == DEVICE_EXIO) {
                ui->WDT_Ftp->show_log(tr("文件发送至EXIO板中..."), tr(""), QColor("red"));
            }
            else if (update_dev == DEVICE_CBM02) {
                ui->WDT_Ftp->show_log(tr("文件正在发送至CBM02板中..."), tr(""), QColor("red"));
            }
            else if (update_dev == DEVICE_MPB01) {
                ui->WDT_Ftp->show_log(tr("文件正在发送至MPB01板中..."), tr(""), QColor("red"));
            }
        }
        //
        change_current_state(UPDATE_BIN, 1000 * 300, true);
    }
    else if (current_state_ == WAIT_UPDATE_COMPLETE) {
        // 在等待更新完成状态下，继续监控进度
        change_current_state(WAIT_UPDATE_COMPLETE, 1000, true);
    }
    else if (current_state_ == HEARTBEAT) {
        change_current_state(HEARTBEAT, 1000, true);
    }
    else if (current_state_ == STOP) {
        change_current_state(STOP, 100, true);
    }

    // 修复后的逻辑：检测上传完成信号消失，进入等待子板更新完成状态
    if (update_end == 1 && (dat[4] & 0xFE) == 0) {
        if (end - start >= 5) {
            update_end = 0;
            // 不再直接显示更新完成，而是进入等待子板更新完成状态
            change_current_state(WAIT_UPDATE_COMPLETE, 1000, true);
            ui->WDT_Ftp->show_log(tr("开始监控子板更新进度..."), tr(""), QColor("orange"));

            // 启动更新超时检测定时器（最大等待时间10分钟）
            update_timeout_timer_.start(600000);
            last_progress_time_ = time(nullptr);
        }
        end = time(nullptr);
    }

    // 更新进度并检查是否完成
    int current_k2_progress = (int)dat[6];
    int current_k3k4_progress = (int)dat[7];
    int current_k20_progress = (int)dat[8];
    int current_k21_progress = (int)dat[9];

    ui->PRB_To_K2->setValue(current_k2_progress);
    ui->PRB_To_K3K4->setValue(current_k3k4_progress);
    ui->PRB_To_K20->setValue(current_k20_progress);
    ui->PRB_To_K21->setValue(current_k21_progress);

    // 检查进度是否有变化
    if (current_k2_progress != last_k2_progress_ ||
        current_k3k4_progress != last_k3k4_progress_ ||
        current_k20_progress != last_k20_progress_ ||
        current_k21_progress != last_k21_progress_) {
        last_progress_time_ = time(nullptr);
        last_k2_progress_ = current_k2_progress;
        last_k3k4_progress_ = current_k3k4_progress;
        last_k20_progress_ = current_k20_progress;
        last_k21_progress_ = current_k21_progress;
    }

    // 在等待更新完成状态下，检查所有子板是否都完成了
    if (current_state_ == WAIT_UPDATE_COMPLETE && update_upload_completed_) {
        bool all_completed = true;

        // 检查各子板进度是否都达到100%
        // 注意：只检查有效的子板（进度大于0的认为是活跃子板）
        if (current_k2_progress > 0 && current_k2_progress < 100) all_completed = false;
        if (current_k3k4_progress > 0 && current_k3k4_progress < 100) all_completed = false;
        if (current_k20_progress > 0 && current_k20_progress < 100) all_completed = false;
        if (current_k21_progress > 0 && current_k21_progress < 100) all_completed = false;

        if (all_completed && !update_all_completed_) {
            update_all_completed_ = true;
            update_timeout_timer_.stop();
            refresh_timer_.start(5000);
            ui->WDT_Ftp->show_log(tr("所有子板更新完成！"), tr(""), QColor("green"));
            change_current_state(HEARTBEAT, 1000, true);
        }
    }
}

int M35Update::pack_data(unsigned char* dat, uint8_t major_id, uint8_t minor_id)
{
    // 启动FTP服务
    if (current_state_ == LAUNCH_FTP_SERVICES) {
        int send_length = 16;
        dat[0]          = 0x86;
        dat[1]          = 0x01;
        dat[2]          = 0x00;
        dat[3]          = 0x10;
        IoDataParser::set_bit(dat, 4, 0, 1);
        change_current_state(1000, false);
        return send_length;
    }
    // 重启控制器(只能重启主控板)
    else if (current_state_ == RESTART_MCU) {
        int send_length = 16;
        dat[0]          = 0x86;
        dat[1]          = 0x01;
        dat[2]          = 0x00;
        dat[3]          = 0x10;
        IoDataParser::set_bit(dat, 4, 1, 1);

        // delay_msec_suspend会阻塞UI, 提前强制处理下当前线程的消息
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
        // 如果不加延时,mcu来不及处理bin文件,会更新失败
        IoDateTime::delay_msec_suspend(3000);
        //
        change_current_state(HEARTBEAT, 1000, false);
        ui->WDT_Ftp->show_log(tr("控制器重启中,请注意查看指示灯"), tr(""), QColor("red"));
        return send_length;
    }
    // 开始发送 (主控板将数据发送到子板)
    else if (current_state_ == UPDATE_BIN) {
        int send_length = 16;
        dat[0]          = 0x86;
        dat[1]          = 0x01;
        dat[2]          = 0x00;
        dat[3]          = 0x10;
        if (update_mcu_ == 1) {
            update_mcu_ = 0;
            IoDataParser::set_bit(dat, 4, 2, 1);
        }
        change_current_state(1000 * 30, false);
        return send_length;
    }
    // 等待更新完成状态 - 发送心跳包继续监控
    else if (current_state_ == WAIT_UPDATE_COMPLETE) {
        int send_length = 16;
        dat[0]          = 0x86;
        dat[1]          = 0x01;
        dat[2]          = 0x00;
        dat[3]          = 0x10;
        change_current_state(1000, false);
        return send_length;
    }
    // 心跳包
    else if (current_state_ == HEARTBEAT) {
        int send_length = 16;
        dat[0]          = 0x86;
        dat[1]          = 0x01;
        dat[2]          = 0x00;
        dat[3]          = 0x10;
        change_current_state(1000, false);
        return send_length;
    }
    else if (current_state_ == STOP) {
        change_current_state(STOP, 100, false);
        return 0;
    }
}

void M35Update::init(QString project)
{
    project_         = project;
    config_file_     = QString("./projects/%1/config.xml").arg(project_);
    update_mcu_      = 0;
    front_device_id_ = 0;

    // 初始化新增的变量
    upload_complete_time_ = 0;
    last_progress_time_ = 0;
    last_k2_progress_ = 0;
    last_k3k4_progress_ = 0;
    last_k20_progress_ = 0;
    last_k21_progress_ = 0;
    update_upload_completed_ = false;
    update_all_completed_ = false;

    ui->WDT_Ftp->init(project_);
    //
    init_ftp();
    init_log_font();
    init_timer();
    init_connect();
    //
    change_current_state(STOP, 1000, true);
}

int M35Update::after_state_changed()
{
    change_remote_port();
    change_current_state(STOP, 1000, true);
    //
    IoEasyLog easylog(project_);
    easylog.log_info("当前模块: HAVC-M35-固件更新");
}

void M35Update::init_connect_parent(QWidget* widget)
{
    if (widget == nullptr) {
        return;
    }
    connect(widget, SIGNAL(sig_timeout_rsend(int)), this, SLOT(slot_timeout_rsend(int)));
    connect(widget, SIGNAL(sig_timeout_rsend_null()), this, SLOT(slot_timeout_rsend_null()));
    connect(widget, SIGNAL(sig_remote_ip_changed(QString)), this, SLOT(slot_remote_ip_changed(QString)));
    connect(widget, SIGNAL(sig_hotkey_alt_del()), this, SLOT(slot_hotkey_alt_del()));
    connect(widget, SIGNAL(sig_communicate_break()), this, SLOT(slot_communicate_break()));
}

void M35Update::init_ftp()
{
    QString ftp_host        = xml_parser_.read_data_xml(config_file_, "REMOTE_IP");
    QString ftp_user        = xml_parser_.read_data_xml(config_file_, "FTP_USER");
    QString ftp_password    = xml_parser_.read_data_xml(config_file_, "FTP_PASSWORD");
    int     ftp_port        = xml_parser_.read_data_xml(config_file_, "FTP_PORT").toInt();
    QString ftp_root_dir    = xml_parser_.read_data_xml(config_file_, "FTP_ROOT_DIR");
    QString ftp_top_dir     = xml_parser_.read_data_xml(config_file_, "FTP_TOP_DIR");
    QString ftp_work_dir    = xml_parser_.read_data_xml(config_file_, "FTP_WORK_DIR");
    QString ftp_del_show    = xml_parser_.read_data_xml(config_file_, "FTP_DEL_SHOW");
    QString ftp_upload_show = xml_parser_.read_data_xml(config_file_, "FTP_UPLOAD_SHOW");

    // 多IP时默认使用第一个IP
    QStringList ip_list = ftp_host.split("|");
    if (ip_list.count() > 0) {
        ftp_host = ip_list.at(0);
    }

    ui->WDT_Ftp->set_host_info(ftp_host, ftp_port);
    ui->WDT_Ftp->set_user_info(ftp_user, ftp_password);
    ui->WDT_Ftp->add_root_dir(ftp_root_dir);
    ui->WDT_Ftp->set_top_dir(ftp_top_dir);
    ui->WDT_Ftp->set_work_dir(ftp_work_dir);
    // 默认隐藏
    if(ftp_del_show == "1"){
        ui->WDT_Ftp->set_del_show(true);
    }
    // 默认显示
    if(ftp_upload_show == "0"){
        ui->WDT_Ftp->set_upload_show(false);
    }
}

void M35Update::init_connect()
{
    // 选中bin文件
    connect(ui->WDT_Ftp, &IoFtpWidget::sig_selected_file, this, [=](QString file) {
        if (file.isEmpty()) {
            return;
        }
        // 获取上传文件名的脚本, 不同项目上传到控制器中的文件名是不同的
        // 把该项目通过脚本开放出来
        QString script = xml_parser_.read_data_xml(config_file_, "GET_REMOTE_NAME_SCRIPT");

        // 取脚本返回的文件名
        QString script_result = IoScriptEngine::call_javascript(script, QStringList(file));
        ui->WDT_Ftp->set_remote_bin_name(script_result);

        //
        IoEasyLog easylog(project_);
        easylog.log_info(tr("HVAC-M35-选择更新固件:"));
        easylog.log_info(file);
        easylog.log_info(tr("HVAC-M35-远程文件名:"));
        easylog.log_info(script_result);
    });

    // 文件上传完毕
    connect(ui->WDT_Ftp, &IoFtpWidget::sig_put_file_done, this, [=]() {
        ui->WDT_Ftp->show_log(tr("文件上传到主控板完成"), tr(""), QColor("green"));
        update_mcu_ = 1;
        // 重置更新状态标志
        update_upload_completed_ = false;
        update_all_completed_ = false;
        last_k2_progress_ = 0;
        last_k3k4_progress_ = 0;
        last_k20_progress_ = 0;
        last_k21_progress_ = 0;
        // delay_msec_suspend会阻塞UI, 提前强制处理下当前线路的消息
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
        IoDateTime::delay_msec_suspend(1000);   // 加延时,防止命令太快来不及处理被覆盖掉
        change_next_state(UPDATE_BIN, 1000, true);
    });

    // 启动Ftp服务
    connect(ui->WDT_Ftp, &IoFtpWidget::sig_ready_connect, this, [=]() {
        change_next_state(LAUNCH_FTP_SERVICES, 1000, true);
        //
        IoEasyLog easylog(project_);
        easylog.log_info(tr("HVAC-M35-启动FTP服务"));
    });

    // 开始更新 (例:主控板 传输到 子板)
    // connect(ui->PB_Update,&QPushButton::clicked,this,[=](){
    //    update_mcu_ = 1;
    //    change_next_state(UPDATE_BIN,1000,true);
    //
    //    //
    //    IoEasyLog easylog(project_);
    //    easylog.log_info(tr("开始传输固件到子板"));
    //});

    // 重启控制板  注: 只能重启主控制板 2023-12-25 17:01:34
    connect(ui->PB_Restart, &QPushButton::clicked, this, [=]() {
        change_next_state(RESTART_MCU, 1000, true);
        //
        IoEasyLog easylog(project_);
        easylog.log_info(tr("重启控制板"));
    });

    //
    connect(&refresh_timer_, &QTimer::timeout, this, [=]() {
        refresh_timer_.stop();
        ui->WDT_Ftp->show_log(tr("固件更新完成，请确认运行指示灯RUN闪烁后断电重启控制器！"), tr(""), QColor("green"));
    });

    // 连接更新超时检测定时器
    connect(&update_timeout_timer_, &QTimer::timeout, this, &M35Update::slot_update_timeout_check);
}

void M35Update::init_log_font()
{
    QFont font;
    font.setFamily("SimSun");
    font.setPixelSize(16);   // 不受缩放影响
    ui->WDT_Ftp->set_log_font(font);
}

void M35Update::init_timer()
{
    refresh_timer_.setTimerType(Qt::PreciseTimer);

    // 初始化更新超时检测定时器
    update_timeout_timer_.setTimerType(Qt::PreciseTimer);
    update_timeout_timer_.setSingleShot(false);
    update_timeout_timer_.setInterval(30000); // 每30秒检查一次
}

void M35Update::change_remote_port()
{
    QVariantMap config_map;
    config_map.insert("remote_port", "8299");
    config_map.insert("local_port", "20181");
    config_map.insert("send_checksum", "CRC16_A001");
    config_map.insert("recv_checksum", "CRC16_A001");
    emit sig_config_changed(config_map);
}

// 单独修改ftp服务器IP
void M35Update::slot_remote_ip_changed(QString remote_ip)
{
    ui->WDT_Ftp->set_host_info(remote_ip);

    //
    IoEasyLog easylog(project_);
    easylog.log_info(tr("HVAC-M35-修改FTP服务器IP"));
    easylog.log_info(remote_ip);
}

void M35Update::slot_hotkey_alt_del()
{
    ui->WDT_Ftp->set_del_visible();
}

void M35Update::slot_communicate_break()
{
    ui->WDT_Ftp->slot_communicate_break();
    qDebug() << "m35 update: 通讯断开";
}

void M35Update::slot_update_timeout_check()
{
    if (current_state_ != WAIT_UPDATE_COMPLETE) {
        update_timeout_timer_.stop();
        return;
    }

    time_t current_time = time(nullptr);

    // 检查是否长时间没有进度更新（超过2分钟）
    if (current_time - last_progress_time_ > 120) {
        update_timeout_timer_.stop();
        ui->WDT_Ftp->show_log(tr("警告：子板更新进度长时间无变化，可能更新失败！"), tr(""), QColor("red"));
        ui->WDT_Ftp->show_log(tr("请检查子板连接状态或手动重试更新"), tr(""), QColor("red"));
        change_current_state(HEARTBEAT, 1000, true);
        return;
    }

    // 检查总超时时间（超过10分钟）
    if (upload_complete_time_ > 0 && current_time - upload_complete_time_ > 600) {
        update_timeout_timer_.stop();
        ui->WDT_Ftp->show_log(tr("错误：子板更新超时，更新失败！"), tr(""), QColor("red"));
        ui->WDT_Ftp->show_log(tr("请检查网络连接和子板状态后重新尝试"), tr(""), QColor("red"));
        change_current_state(HEARTBEAT, 1000, true);
        return;
    }

    // 显示当前等待状态
    int elapsed_time = current_time - upload_complete_time_;
    ui->WDT_Ftp->show_log(tr("等待子板更新中... 已等待 %1 秒").arg(elapsed_time), tr(""), QColor("blue"));
}
