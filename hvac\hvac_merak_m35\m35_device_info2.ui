<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>M35DeviceInfo2</class>
 <widget class="QWidget" name="M35DeviceInfo2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1454</width>
    <height>896</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QLabel{
	font-family: &quot;宋体&quot;; 
	font-size: 14px; 
	min-height: 25px;
}

QLineEdit{
	font-size: 14px; 
	min-height: 25px;
	min-width: 120px;
	max-width: 120px;
}

QPushButton{
	border: 1px solid #7f8585; 
	border-radius: 3px; 	
	font-size: 14px;
	min-height: 28px;

	background-color: rgb(215, 215, 215)
}

QPushButton:hover {
	background-color: #4b6e8a; 
	color:#ffffff;
}

QGroupBox{
	border:1px solid #7f8585;
	font-size:14px;
	border-radius: 3px; 
	margin-top: 9px;    
	min-width:450px;
}
QGroupBox::title{
	subcontrol-origin: margin;
	subcontrol-position: top center;
	padding: 0 3px;
	font-weight: bold; 
	font-size: 14px; 
}

#LB_K1_Nand,#LB_K1_Nor,#LB_K1_State,#LB_K1_Error,#LB_K1_Hse{
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background:white;
	border:2px solid #8B8989;
}
#LB_K2_Nor,#LB_K2_State,#LB_K2_Error,#LB_K2_Hse{
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background:white;
	border:2px solid #8B8989;
}
#LB_K3_Nor,#LB_K3_State,#LB_K3_Error,#LB_K3_Hse{
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background:white;
	border:2px solid #8B8989;
}
#LB_K20_Nor,#LB_K20_State,#LB_K20_Error,#LB_K20_Hse{
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background:white;
	border:2px solid #8B8989;
}
#LB_K21_Nor,#LB_K21_State,#LB_K21_Error,#LB_K21_Hse{
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background:white;
	border:2px solid #8B8989;
}
#LB_K4_Nor,#LB_K4_State,#LB_K4_Error,#LB_K4_Hse{
	min-width:20px;
	max-width:20px;
	min-height:20px;
	max-height:20px;
	border-radius:11px;
	background:white;
	border:2px solid #8B8989;
}
#GB_K1,#GB_K2,#GB_K3,#GB_K20,#GB_K21,#GB_K4{
	min-height:230px;
	max-height:230px;
}
</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_5">
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>5</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <property name="spacing">
    <number>5</number>
   </property>
   <item row="0" column="0">
    <widget class="QGroupBox" name="GB_Control">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="title">
      <string>主控板</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_11" rowstretch="1,0,0,0">
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>5</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <property name="spacing">
       <number>5</number>
      </property>
      <item row="1" column="0">
       <widget class="QGroupBox" name="GB_K2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>452</width>
          <height>241</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>241</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>K2单板</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <layout class="QGridLayout" name="gridLayout_6">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <property name="spacing">
          <number>5</number>
         </property>
         <item row="2" column="1">
          <widget class="QLineEdit" name="LE_K2_Fault"/>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="LB_K2_Fault">
           <property name="text">
            <string>故障码:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QLabel" name="LB_K2_Boot">
           <property name="text">
            <string>BOOT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="2">
          <widget class="QLabel" name="LB_K2_Id">
           <property name="text">
            <string>板卡ID:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="LB_K2_Hw">
           <property name="text">
            <string>HW-VER:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="LE_K2_Hw"/>
         </item>
         <item row="2" column="3">
          <widget class="QLineEdit" name="LE_K2_Boot"/>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="LE_K2_Cpld"/>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="LB_K2_Cpld">
           <property name="text">
            <string>CPLD-VER:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QLineEdit" name="LE_K2_Id"/>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="LB_K2_Bl">
           <property name="text">
            <string>BL-VER:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="2">
          <widget class="QLabel" name="LB_K2_5v">
           <property name="text">
            <string>板内5V:</string>
           </property>
          </widget>
         </item>
         <item row="5" column="1">
          <widget class="QLineEdit" name="LE_K2_Mvb"/>
         </item>
         <item row="4" column="3">
          <widget class="QLineEdit" name="LE_K2_5v"/>
         </item>
         <item row="0" column="0" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <widget class="QLabel" name="LB_K2_Nor">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K2_Nor_Lab">
             <property name="text">
              <string>Nor Flash</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K2_Hse">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K2_Hse_Lab">
             <property name="text">
              <string>HSE</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="2" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QLabel" name="LB_K2_State">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K2_State_Lab">
             <property name="text">
              <string>通讯灯</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K2_Error">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K2_Error_Lab">
             <property name="text">
              <string>故障灯</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="6" column="1">
          <spacer name="verticalSpacer_4">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QGroupBox" name="GB_K1">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>452</width>
          <height>241</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>241</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>K1单板</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <property name="spacing">
          <number>5</number>
         </property>
         <item row="0" column="0" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QLabel" name="LB_K1_Nand">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K1_Nor_Lab">
             <property name="text">
              <string>Nor Flash</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K1_Nor">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K1_Nand_Lab">
             <property name="text">
              <string>Nand Flash</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K1_Hse">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K1_Hse_Lab">
             <property name="text">
              <string>HSE</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="2" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_5">
           <item>
            <widget class="QLabel" name="LB_K1_State">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K1_State_Lab">
             <property name="text">
              <string>通讯灯</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K1_Error">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K1_Error_Lab">
             <property name="text">
              <string>故障灯</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="LE_K1_Fault"/>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="LB_K1_Fault">
           <property name="text">
            <string>故障码:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="LE_K1_Hw"/>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="LB_K1_HW">
           <property name="text">
            <string>HW-VER:</string>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QLineEdit" name="LE_K1_Boot"/>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="LB_K1_Boot">
           <property name="text">
            <string>BOOT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="LB_K1_Cpld">
           <property name="text">
            <string>CPLD-VER:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QLabel" name="LB_K1_Ip">
           <property name="text">
            <string>IP地址:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="3">
          <widget class="QLineEdit" name="LE_K1_Ip"/>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="LE_K1_Cpld"/>
         </item>
         <item row="3" column="2">
          <widget class="QLabel" name="LB_K1_Id">
           <property name="text">
            <string>板卡ID:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QLineEdit" name="LE_K1_Id"/>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="LB_K1_Mvb">
           <property name="text">
            <string>MVB-VER:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="LE_K1_Mvb"/>
         </item>
         <item row="4" column="2">
          <widget class="QLabel" name="LB_K1_5v">
           <property name="text">
            <string>板内5V:</string>
           </property>
          </widget>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="LB_K1_MvbBoot">
           <property name="text">
            <string>MVBBOOT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="3">
          <widget class="QLineEdit" name="LE_K1_5v"/>
         </item>
         <item row="5" column="1">
          <widget class="QLineEdit" name="LE_K1_MvbBoot"/>
         </item>
         <item row="5" column="3">
          <widget class="QLineEdit" name="LE_K1_Rtc"/>
         </item>
         <item row="5" column="2">
          <widget class="QLabel" name="LB_K1_Rtc">
           <property name="text">
            <string>RTC时钟:</string>
           </property>
          </widget>
         </item>
         <item row="6" column="3">
          <widget class="QPushButton" name="PB_K1_Rtc">
           <property name="text">
            <string>同步时钟</string>
           </property>
          </widget>
         </item>
         <item row="7" column="3">
          <spacer name="verticalSpacer_5">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QGroupBox" name="GB_K3">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>452</width>
          <height>241</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>241</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>K3单板</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <layout class="QGridLayout" name="gridLayout_9">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <property name="spacing">
          <number>5</number>
         </property>
         <item row="2" column="2">
          <widget class="QLabel" name="LB_K3_Hw">
           <property name="text">
            <string>HW-VER:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="LB_K3_Fault">
           <property name="text">
            <string>故障码:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="LE_K3_Fault"/>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="LE_K3_Cpld"/>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="LB_K3_Cpld">
           <property name="text">
            <string>CPLD-VER:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="3">
          <widget class="QLineEdit" name="LE_K3_Hw"/>
         </item>
         <item row="3" column="2">
          <widget class="QLabel" name="LB_K3_Bl">
           <property name="text">
            <string>BL-VER:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="LE_K3_Boot"/>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="LB_K3_Boot">
           <property name="text">
            <string>BOOT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QLineEdit" name="LE_K3_Bl"/>
         </item>
         <item row="4" column="2">
          <widget class="QLabel" name="LB_K3_Id">
           <property name="text">
            <string>板内ID:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="3">
          <widget class="QLineEdit" name="LE_K3_Id"/>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="LB_K3_5v">
           <property name="text">
            <string>板内5V:</string>
           </property>
          </widget>
         </item>
         <item row="5" column="1">
          <widget class="QLineEdit" name="LE_K3_5v"/>
         </item>
         <item row="0" column="0" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_6">
           <item>
            <widget class="QLabel" name="LB_K3_Nor">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K3_Nor_Lab">
             <property name="text">
              <string>Nor Flash</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K3_Hse">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K3_Hse_Lab">
             <property name="text">
              <string>HSE</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="2" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_7">
           <item>
            <widget class="QLabel" name="LB_K3_State">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K3_State_Lab">
             <property name="text">
              <string>通讯灯</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K3_Error">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K3_Error_Lab">
             <property name="text">
              <string>故障灯</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="6" column="1">
          <spacer name="verticalSpacer_6">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item row="3" column="0">
       <spacer name="verticalSpacer_2">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QGroupBox" name="GB_Extern">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="title">
      <string>扩展板</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_4">
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>5</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <property name="spacing">
       <number>5</number>
      </property>
      <item row="3" column="0">
       <widget class="QGroupBox" name="GB_Node">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>452</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>EXIO板卡设置ID</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_13" stretch="1,1,1">
         <property name="spacing">
          <number>5</number>
         </property>
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QLabel" name="LB_Current_Id_Lab">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>25</height>
              </size>
             </property>
             <property name="text">
              <string>当前ID:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_Current_Id">
             <property name="text">
              <string>0</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_12">
           <item>
            <widget class="QLabel" name="LB_Node_Lab">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>25</height>
              </size>
             </property>
             <property name="text">
              <string>设置ID:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="LE_Node">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="text">
              <string>0</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QPushButton" name="PB_Set_Node">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>30</height>
            </size>
           </property>
           <property name="text">
            <string>设置</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QGroupBox" name="GB_Exio01">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>452</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>EXIO01</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <layout class="QGridLayout" name="gridLayout_2">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>9</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <property name="spacing">
          <number>5</number>
         </property>
         <item row="0" column="3">
          <widget class="QLineEdit" name="LE_Exio01_Soft"/>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="LB_Exio01_Soft_Lab">
           <property name="text">
            <string>SOFT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="LB_Exio01_Boot_Lab">
           <property name="text">
            <string>BOOT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="LE_Exio01_Boot"/>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QGroupBox" name="GB_Exio02">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>452</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>EXIO02</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <layout class="QGridLayout" name="gridLayout_3">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="topMargin">
          <number>9</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <property name="spacing">
          <number>5</number>
         </property>
         <item row="0" column="3">
          <widget class="QLineEdit" name="LE_Exio02_Soft"/>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="LB_Exio02_Id_Lab">
           <property name="text">
            <string>SOFT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="LB_Exio02_Boot_Lab">
           <property name="text">
            <string>BOOT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="LE_Exio02_Boot"/>
         </item>
        </layout>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QGroupBox" name="GB_K4">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>452</width>
          <height>241</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>241</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>K4单板</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <layout class="QGridLayout" name="gridLayout_15">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <property name="spacing">
          <number>5</number>
         </property>
         <item row="1" column="0">
          <widget class="QLabel" name="LB_K4_Fault">
           <property name="text">
            <string>故障码:</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="LE_K4_Fault"/>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="LB_K4_Boot">
           <property name="text">
            <string>BOOT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="LB_K4_Hw">
           <property name="text">
            <string>HW-VER:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="LE_K4_Cpld"/>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="LB_K4_Cpld">
           <property name="text">
            <string>CPLD-VER:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="LE_K4_Hw"/>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="LE_K4_Mvb"/>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="LB_K4_Mvb">
           <property name="text">
            <string>MVB-VER:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QLabel" name="LB_K20_Id_2">
           <property name="text">
            <string>板卡ID:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QLineEdit" name="LE_K4_5v"/>
         </item>
         <item row="2" column="3">
          <widget class="QLineEdit" name="LE_K4_Id"/>
         </item>
         <item row="3" column="2">
          <widget class="QLabel" name="LB_K4_5v">
           <property name="text">
            <string>板内5V:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_14">
           <item>
            <widget class="QLabel" name="LB_K4_Nor">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K4_Nor_Lab">
             <property name="text">
              <string>Nor Flash</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K4_Hse">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K4_Hse_Lab">
             <property name="text">
              <string>HSE</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="1" column="3">
          <widget class="QLineEdit" name="LE_K4_Boot"/>
         </item>
         <item row="0" column="2" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_15">
           <item>
            <widget class="QLabel" name="LB_K4_State">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K4_State_Lab">
             <property name="text">
              <string>通讯灯</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K4_Error">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K4_Error_Lab">
             <property name="text">
              <string>故障灯</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="5" column="1">
          <spacer name="verticalSpacer_9">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item row="4" column="0">
       <spacer name="verticalSpacer_3">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QGroupBox" name="GB_Driver">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="title">
      <string>司机窒板</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_13" rowstretch="1,0,0">
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>5</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <property name="spacing">
       <number>5</number>
      </property>
      <item row="0" column="0">
       <widget class="QGroupBox" name="GB_K20">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>452</width>
          <height>241</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>241</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>K20单板</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <layout class="QGridLayout" name="gridLayout_10">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <property name="spacing">
          <number>5</number>
         </property>
         <item row="2" column="2">
          <widget class="QLabel" name="LB_K20_Id">
           <property name="text">
            <string>板卡ID:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="3">
          <widget class="QLineEdit" name="LE_K20_Rtc"/>
         </item>
         <item row="3" column="3">
          <widget class="QLineEdit" name="LE_K20_5v"/>
         </item>
         <item row="2" column="3">
          <widget class="QLineEdit" name="LE_K20_Id"/>
         </item>
         <item row="4" column="2">
          <widget class="QLabel" name="LB_K20_Rtc">
           <property name="text">
            <string>RTC时钟:</string>
           </property>
          </widget>
         </item>
         <item row="5" column="3">
          <widget class="QPushButton" name="PB_K20_Rtc">
           <property name="text">
            <string>同步时钟</string>
           </property>
          </widget>
         </item>
         <item row="3" column="2">
          <widget class="QLabel" name="LB_K20_5v">
           <property name="text">
            <string>板内5V:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_8">
           <item>
            <widget class="QLabel" name="LB_K20_Nor">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K20_Nor_Lab">
             <property name="text">
              <string>Nor Flash</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K20_Hse">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K20_Hse_Lab">
             <property name="text">
              <string>HSE</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="2" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_9">
           <item>
            <widget class="QLabel" name="LB_K20_State">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K20_State_Lab">
             <property name="text">
              <string>通讯灯</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K20_Error">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K20_Error_Lab">
             <property name="text">
              <string>故障灯</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="LB_K20_Fault">
           <property name="text">
            <string>故障码:</string>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="LB_K20_Boot">
           <property name="text">
            <string>BOOT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QLineEdit" name="LE_K20_Boot"/>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="LE_K20_Fault"/>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="LE_K20_Hw"/>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="LE_K20_Cpld"/>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="LB_K20_Hw">
           <property name="text">
            <string>HW-VER:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="LB_K20_Cpld">
           <property name="text">
            <string>CPLD-VER:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="LB_K20_Mvb">
           <property name="text">
            <string>MVB-VER:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="LE_K20_Mvb"/>
         </item>
         <item row="6" column="3">
          <spacer name="verticalSpacer_7">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QGroupBox" name="GB_K21">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>452</width>
          <height>241</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>241</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="title">
         <string>K21单板</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <layout class="QGridLayout" name="gridLayout_12">
         <property name="leftMargin">
          <number>5</number>
         </property>
         <property name="rightMargin">
          <number>5</number>
         </property>
         <property name="bottomMargin">
          <number>5</number>
         </property>
         <property name="spacing">
          <number>5</number>
         </property>
         <item row="0" column="0" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_10">
           <item>
            <widget class="QLabel" name="LB_K21_Nor">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K21_Nor_Lab">
             <property name="text">
              <string>Nor Flash</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K21_Hse">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K21_Hse_Lab">
             <property name="text">
              <string>HSE</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="5" column="1">
          <widget class="QLineEdit" name="LE_K21_Mvb"/>
         </item>
         <item row="0" column="2" colspan="2">
          <layout class="QHBoxLayout" name="horizontalLayout_11">
           <item>
            <widget class="QLabel" name="LB_K21_State">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K21_State_Lab">
             <property name="text">
              <string>通讯灯</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K21_Error">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="LB_K21_Error_Lab">
             <property name="text">
              <string>故障灯</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="LB_K21_Fault">
           <property name="text">
            <string>故障码:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <widget class="QLabel" name="LB_K21_Boot">
           <property name="text">
            <string>BOOT-VER:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="LE_K21_Fault"/>
         </item>
         <item row="2" column="3">
          <widget class="QLineEdit" name="LE_K21_Boot"/>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="LB_K21_Hw">
           <property name="text">
            <string>HW-VER:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="LE_K21_Hw"/>
         </item>
         <item row="3" column="2">
          <widget class="QLabel" name="LB_K21_Id">
           <property name="text">
            <string>板卡ID:</string>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QLineEdit" name="LE_K21_Id"/>
         </item>
         <item row="4" column="2">
          <widget class="QLabel" name="LB_K21_5v">
           <property name="text">
            <string>板内5V:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="LB_K21_Cpld">
           <property name="text">
            <string>CPLD-VER:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="LE_K21_Cpld"/>
         </item>
         <item row="4" column="3">
          <widget class="QLineEdit" name="LE_K21_5v"/>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="LB_K21_Bl">
           <property name="text">
            <string>BL-VER:</string>
           </property>
          </widget>
         </item>
         <item row="6" column="1">
          <spacer name="verticalSpacer_8">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="0">
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>259</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
