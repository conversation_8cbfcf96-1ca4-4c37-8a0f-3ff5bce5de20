﻿#include "m35_di_do.h"
#include "ui_m35_di_do.h"
#include <QDebug>

// 不同板卡使用的功能码也不同, 请查看通讯协议文件
static uint8_t g_command_id = 0;

M35DiDo::M35DiDo(QWidget *parent) : IoBaseControls(parent),
    ui(new Ui::M35DiDo)
{
    ui->setupUi(this);
}

M35DiDo::~M35DiDo()
{
    delete ui;
}

//-------------------------------------------------
// 通讯数据相关
// 参考M35项目通讯协议
void M35DiDo::parse_data(unsigned char *dat, int len, void *buffer, int buffer_len)
{
    if(current_state_ == GET_DI_DO){
        if(dat[0] != 0xC2 || dat[1] != g_command_id){
            return;
        }
        // 取当前page指针显示数据
        IoBaseForm* current_page = static_cast<IoBaseForm*>(ui->tabWidget->currentWidget());
        if(current_page != nullptr){
            current_page->parse_data(dat,len,nullptr,0);
        }
        //
        change_current_state(GET_DI_DO,1000,true);
    }
}

int M35DiDo::pack_data(unsigned char *dat,uint8_t major_id,uint8_t minor_id)
{
    if(current_state_ == GET_DI_DO){
        // page的顺序 与 通讯协议中功能码的顺序 对应
        g_command_id = static_cast<uint8_t>(ui->tabWidget->currentIndex() + 1);
        //
        dat[0] = 0x82;
        dat[1] = g_command_id;
        dat[2] = 0x00;
        dat[3] = 0x40;
        //
        IoBaseForm* current_page = static_cast<IoBaseForm*>(ui->tabWidget->currentWidget());
        if(current_page != nullptr){
            current_page->pack_data(dat,major_id,minor_id);
        }
        //
        change_current_state(1000,false);
        //
        int send_length = 64;
        return send_length;
    }
    else{
        return 0;
    }
}
//-------------------------------------------------
// 初始化相关
void M35DiDo::init(QString project)
{
    project_ = project;
    //
    dio_control_xml_ = QString("./projects/%1/xml/m35_dio_control.xml").arg(project_);
    dio_driver_xml_ = QString("./projects/%1/xml/m35_dio_driver.xml").arg(project_);
    dio_extend_xml_ = QString("./projects/%1/xml/m35_dio_extend.xml").arg(project_);
    dio_bgd_xml_ = QString("./projects/%1/xml/m35_dio_bgd.xml").arg(project_);
    dio_exio_xml_ = QString("./projects/%1/xml/m35_dio_exio.xml").arg(project_);
    //
    ui->WDT_Control->init(project_,dio_control_xml_);
    ui->WDT_Driver->init(project_,dio_driver_xml_);
    ui->WDT_Extend->init(project_,dio_extend_xml_);
    ui->WDT_Bgd->init(project_,dio_bgd_xml_);
    ui->WDT_Exio->init(project_,dio_exio_xml_);
    //
    change_current_state(GET_DI_DO,1000,true);
}

//-------------------------------------------------
// 切换模块相关
int M35DiDo::after_state_changed()
{
    change_current_state(GET_DI_DO,1000,true);

    IoEasyLog easylog(project_);
    easylog.log_info("当前模块: HAVC-M35-DIDO");
}
