<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>M35DiDo</class>
 <widget class="QWidget" name="M35DiDo">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1173</width>
    <height>640</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QTabBar::tab
{
	height:32;
	width:122;
}

QTabWidget#tabWidget
{
	/* background-color:rgb(255,0,0);*/
}
QTabBar::tab
{
	/* 
	background-color:rgb(220,200,180);
	color:rgb(0,0,0);
	*/
}
QTabBar::tab::selected
{
	background-color:#44b5df;
	color:#ffffff;
	font:10pt '新宋体'
}
</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>5</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <property name="spacing">
    <number>5</number>
   </property>
   <item row="0" column="0">
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="IoBaseForm" name="WDT_Control">
      <attribute name="title">
       <string>主控板</string>
      </attribute>
      <layout class="QFormLayout" name="formLayout">
       <property name="horizontalSpacing">
        <number>5</number>
       </property>
       <property name="verticalSpacing">
        <number>5</number>
       </property>
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
      </layout>
     </widget>
     <widget class="IoBaseForm" name="WDT_Driver">
      <attribute name="title">
       <string>司机窒板</string>
      </attribute>
     </widget>
     <widget class="IoBaseForm" name="WDT_Extend">
      <attribute name="title">
       <string>扩展板</string>
      </attribute>
     </widget>
     <widget class="IoBaseForm" name="WDT_Bgd">
      <attribute name="title">
       <string>BGD板</string>
      </attribute>
     </widget>
     <widget class="IoBaseForm" name="WDT_Exio">
      <attribute name="title">
       <string>EXIO板</string>
      </attribute>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>IoBaseForm</class>
   <extends>QWidget</extends>
   <header>../io_custom_component/io_base_form/io_base_form.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
