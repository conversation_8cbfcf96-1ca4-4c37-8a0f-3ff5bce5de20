﻿#ifndef M35_AI_AO_H
#define M35_AI_AO_H

#include "hvac_global.h"
#include <QWidget>
#include <QTranslator>


#include "../io_easy_log/io_easy_log.h"
#include "../io_datetime/io_datetime.h"
#include "../io_xml_parser/io_xml_parser.h"
#include "../io_base_controls/io_base_controls.h"
#include "../io_custom_component/io_base_form/io_base_form.h"

namespace Ui {
class M35AiAo;
}

class HVACSHARED_EXPORT M35AiAo : public IoBaseControls
{
    Q_OBJECT

public:
    explicit M35AiAo(QWidget *parent = 0);
    ~M35AiAo();

private:
    Ui::M35AiAo *ui;

public:
    // 通讯状态机
    enum COMMUNICATE_STATE{
        GET_AI_AO,
        STOP
    };

    void init(QString project);
    //-------------------------------------------------
    void parse_data(unsigned char* dat,int len,void* buffer,int buffer_len) override;
    int pack_data(unsigned char* dat,uint8_t major_id,uint8_t minor_id) override;
    //-------------------------------------------------
    int after_state_changed() override;



private:
    QString project_;
    //
    QString aio_control_xml_;
    QString aio_driver_xml_;
    QString aio_extend_xml_;
    QString aio_bgd_xml_;
    QString aio_exio_xml_;
    //-------------------------------------------------
    IoXmlParser xml_parser_;
};

#endif // KTX_DI_DO_H
