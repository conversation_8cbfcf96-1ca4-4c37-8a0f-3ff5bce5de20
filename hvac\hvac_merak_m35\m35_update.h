﻿#ifndef M35_UPDATE_H
#define M35_UPDATE_H

#include "hvac_global.h"
#include <QTimer>
#include <QWidget>
#include <QTranslator>


#include "../io_easy_log/io_easy_log.h"
#include "../io_xml_parser/io_xml_parser.h"
#include "../io_data_parser/io_data_parser.h"
#include "../io_base_controls/io_base_controls.h"
#include "../io_custom_component/io_ftp_widget/io_ftp_widget.h"


namespace Ui {
class M35Update;
}

class HVACSHARED_EXPORT M35Update : public IoBaseControls
{
    Q_OBJECT

public:
    explicit M35Update(QWidget *parent = 0);
    ~M35Update();

private:
    Ui::M35Update *ui;

public:
    // 通讯状态机
    enum COMMUNICATE_STATE
    {
        LAUNCH_FTP_SERVICES,
        RESTART_MCU,
        UPDATE_BIN,
        WAIT_UPDATE_COMPLETE,  // 新增：等待所有子板更新完成
        HEARTBEAT,
        STOP
    };

    void init(QString project);
    //-------------------------------------------------
    void parse_data(unsigned char* dat,int len,void* buffer,int buffer_len) override;
    int pack_data(unsigned char* dat,uint8_t major_id,uint8_t minor_id) override;
    //-------------------------------------------------
    int after_state_changed() override;
    void init_connect_parent(QWidget* widget) override;

private:
    void init_ftp();
    void init_connect();
    void init_log_font();
    void init_timer();

    void update_config();
    void change_remote_port();

signals:
    void sig_ftp_started();
    void sig_hotkey_alt_del();
    void sig_communicate_break();

private slots:
    void slot_remote_ip_changed(QString remote_ip);
    void slot_hotkey_alt_del();
    void slot_communicate_break();
    void slot_update_timeout_check();  // 新增：更新超时检查槽函数

private:
    QString project_;
    QString config_file_;
    IoXmlParser xml_parser_;
    QTimer refresh_timer_;              // 发送到子板完成, 5秒后提示信息
    QTimer update_timeout_timer_;       // 更新超时检测定时器
    int update_mcu_;
    uint8_t front_device_id_;           // 通讯协议中定义为设备更新id, 实际代码中做为[更新完成]的反馈位用

    // 新增：更新进度监控相关
    time_t upload_complete_time_;       // 上传完成时间
    time_t last_progress_time_;         // 最后一次进度更新时间
    int last_k2_progress_;              // 上次K2进度
    int last_k3k4_progress_;            // 上次K3K4进度
    int last_k20_progress_;             // 上次K20进度
    int last_k21_progress_;             // 上次K21进度
    bool update_upload_completed_;      // 文件上传完成标志
    bool update_all_completed_;         // 所有子板更新完成标志

};

#endif // KTX_UPDATE_H
