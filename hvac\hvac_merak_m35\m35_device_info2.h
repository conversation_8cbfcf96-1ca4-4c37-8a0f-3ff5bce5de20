﻿#ifndef M35_DEVICE_INFO2_H
#define M35_DEVICE_INFO2_H

#include "hvac_global.h"
#include <QWidget>
#include <QTimer>
#include <QTranslator>

#include "../io_easy_log/io_easy_log.h"
#include "../io_datetime/io_datetime.h"
#include "../io_xml_parser/io_xml_parser.h"
#include "../io_data_parser/io_data_parser.h"
#include "../io_script_engine/io_script_engine.h"
#include "../io_base_controls/io_base_controls.h"

namespace Ui {
class M35DeviceInfo2;
}

class HVACSHARED_EXPORT M35DeviceInfo2 : public IoBaseControls
{
    Q_OBJECT

public:
    explicit M35DeviceInfo2(QWidget *parent = 0);
    ~M35DeviceInfo2();

private:
    Ui::M35DeviceInfo2 *ui;

public:
    // 通讯状态机
    enum COMMUNICATE_STATE
    {
        GET_DEVICE_INFO1,
        GET_DEVICE_INFO2,
        SET_NODE_ID,
        SET_K1_RTC,
        SET_K20_RTC,
        STOP
    };

#pragma pack(push, 1)

    // 取各板卡状态
    struct DEVICE_INFO1_REQUEST{
        uint8_t head;
        uint8_t command;
        uint16_t size;
        // 04
        uint8_t space_04[10];
        uint16_t crc16;
    };
    //
    struct DEVICE_INFO1_RESPONSE{
        uint8_t head;
        uint8_t command;
        uint16_t size;
        // 04 (偏移)
        uint8_t k1_can2     : 1;
        uint8_t k1_485_0    : 1;
        uint8_t k1_485_1    : 1;
        uint8_t k1_ttl      : 1;
        uint8_t k1_232      : 1;
        uint8_t k1_eth      : 1;
        uint8_t k1_mvb      : 1;
        uint8_t space_04    : 1;
        // 05
        uint8_t k2_can      : 1;
        uint8_t k2_485_0    : 1;
        uint8_t k2_485_1    : 1;
        uint8_t space_05    : 5;
        // 06
        uint8_t k3_can      : 1;
        uint8_t space_06    : 7;
        // 07
        uint8_t k4_can      : 1;
        uint8_t space_07    : 7;
        // 08
        uint8_t k20_can     : 1;
        uint8_t k20_485_0   : 1;
        uint8_t k20_485_1   : 1;
        uint8_t space_08    : 5;
        // 09
        uint8_t k21_can     : 1;
        uint8_t space_09    : 7;
        //
        uint32_t sapce_10;                  // 10-13
        //
        uint16_t k1_soft_ver;               // 14-15
        uint16_t k2_soft_ver;               // 16-17
        uint16_t k3_soft_ver;               // 18-19
        uint16_t k4_soft_ver;               // 20-21
        uint16_t k20_soft_ver;              // 22-23
        uint16_t k21_soft_ver;              // 24-25
        uint16_t cbm_soft_ver;              // 26-27
        uint16_t mpb_soft_ver;              // 28-29
        uint16_t exio1_soft_ver;            // 30-31
        uint16_t exio2_soft_ver;            // 32-33
        //
        uint32_t space_34;                  // 34-37
        //
        uint16_t k1_app_ver;                // 38-39
        uint16_t k20_app_ver;               // 40-41
        uint16_t cbm_hardware_ver;          // 42-43
        uint16_t comm02_hardware_ver;       // 44-45
        uint16_t cbm_ad1_soft_ver;          // 46-47
        uint16_t cbm_ad2_soft_ver;          // 48-49
        //
        uint16_t crc16;
    };

    // 取各板卡信息  例:软件版本等
    struct DEVICE_INFO2_REQUEST{
        uint8_t head;
        uint8_t command;
        uint16_t size;
        // 04 (偏移)
        uint8_t k1_rtc_syn      : 1;
        uint8_t space_04_1      : 3;
        uint8_t k20_rtc_syn     : 1;
        uint8_t space_04_2      : 2;
        uint8_t node_set        : 1;
        // 05
        uint8_t node;
        //
        uint32_t local_rtc;     // 06-09
        uint32_t space_10;      // 10-13
        uint16_t crc16;         // 14-15
    };
    //
    struct DEVICE_INFO2_RESPONSE{
        uint8_t head;
        uint8_t command;
        uint16_t size;
        // 04 (偏移)
        uint8_t k1_id           : 4;
        uint8_t type_1          : 4;
        // 05
        uint8_t k1_nor          : 1;
        uint8_t k1_nand         : 1;
        uint8_t k1_hse          : 1;
        uint8_t space_5         : 5;
        //
        uint16_t k1_5v;         // 06-07
        uint32_t k1_rtc;        // 08-11
        uint32_t k1_ipv4;       // 12-15
        // 16
        uint8_t k2_id           : 4;
        uint8_t type_2          : 4;
        // 17
        uint8_t k2_nor          : 1;
        uint8_t space_17_0      : 1;
        uint8_t k2_hse          : 1;
        uint8_t space_17_1      : 5;
        //
        uint16_t k2_5v;         // 18-19
        // 20
        uint8_t k3_id           : 4;
        uint8_t type_3          : 4;
        // 21
        uint8_t k3_nor          : 1;
        uint8_t space_21_0      : 1;
        uint8_t k3_hse          : 1;
        uint8_t space_21_1      : 5;
        //
        uint16_t k3_5v;         // 22-23
        // 24
        uint8_t k4_id           : 4;
        uint8_t type_4          : 4;
        // 25
        uint8_t k4_nor          : 1;
        uint8_t space_25_0      : 1;
        uint8_t k4_hse          : 1;
        uint8_t space_25_1      : 5;
        //
        uint16_t k4_5v;         // 26-27
        // 28
        uint8_t k20_id          : 4;
        uint8_t type_5          : 4;
        // 29
        uint8_t k20_nor         : 1;
        uint8_t space_29_0      : 1;
        uint8_t k20_hse         : 1;
        uint8_t space_29_1      : 5;
        // 30-31
        uint16_t k20_5v;        // 30-31
        uint32_t k20_rtc;       // 32-35
        // 36
        uint8_t k21_id          : 4;
        uint8_t type_6          : 4;
        // 37
        uint8_t k21_nor         : 1;
        uint8_t space_37_0      : 1;
        uint8_t k21_hse         : 1;
        uint8_t space_37_1      : 1;
        //
        uint8_t node            : 4;
        //
        uint16_t k21_5v;            // 38-39
        uint16_t k1_boot_ver;       // 40-41
        uint16_t k1_cpld_ver;       // 42-43
        uint16_t k2_boot_ver;       // 44-45
        uint16_t k2_cpld_ver;       // 46-47
        uint16_t k3_boot_ver;       // 48-49
        uint16_t k3_cpld_ver;       // 50-51
        uint16_t k4_boot_ver;       // 52-53
        uint16_t k4_cpld_ver;       // 54-55
        uint16_t k20_boot_ver;      // 56-57
        uint16_t k20_cpld_ver;      // 58-59
        uint16_t k21_boot_ver;      // 60-61
        uint16_t k21_cpld_ver;      // 62-63
        uint16_t mvb_boot_ver;      // 64-65
        uint16_t mvb_soft_ver;      // 66-67
        uint16_t cbm_soft_ver;      // 68-69
        uint16_t space_70;          // 70-71
        uint16_t exio1_boot_ver;    // 72-73
        uint16_t exio2_boot_ver;    // 74-75
        uint16_t space_76;          // 76-77
        uint16_t crc16;             // 78-79
    };
#pragma pack(pop)

    //-------------------------------------------------
    void init(QString project);
    void parse_data(unsigned char* dat,int len,void* buffer,int buffer_len) override;
    int pack_data(unsigned char* dat,uint8_t major_id,uint8_t minor_id) override;

    int after_state_changed() override;

private:
    void init_widget();
    void init_connect();

    // 发送请求帧
    int pack_device_info1(unsigned char* dat);
    int pack_device_info2(unsigned char* dat);
    int pack_set_id(unsigned char* dat);
    int pack_set_k1_rtc(unsigned char* dat);
    int pack_set_k20_rtc(unsigned char* dat);

    // 解析应答帧
    void parse_device_info1(unsigned char* dat,int len);
    void parse_device_info2(unsigned char* dat,int len);
    //
    void parse_k1_state();
    void parse_k2_state();
    void parse_k3_state();
    void parse_k4_state();
    void parse_k20_state();
    void parse_k21_state();
    //
    void parse_k1_boot();
    void parse_k2_boot();
    void parse_k3_boot();
    void parse_k4_boot();
    void parse_k20_boot();
    void parse_k21_boot();
    void parse_exio1_boot();
    void parse_exio2_boot();
    void parse_node();
    //
    void parse_exio1_soft();
    void parse_exio2_soft();
private:
    QString project_;
    QTimer timer_;

    DEVICE_INFO1_REQUEST device_info1_request_;
    DEVICE_INFO1_RESPONSE device_info1_response_;

    DEVICE_INFO2_REQUEST device_info2_request_;
    DEVICE_INFO2_RESPONSE device_info2_response_;
};

#endif // KTX_DEVICE_INFO_H
