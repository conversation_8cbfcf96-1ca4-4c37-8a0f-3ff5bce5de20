#include "m35_parsing_log.h"
#include "ui_m35_parsing_log.h"

M35ParsingLog::M35ParsingLog(QWidget* parent)
    : IoBaseControls(parent)
    , ui(new Ui::M35ParsingLog)
{
    ui->setupUi(this);
}

M35ParsingLog::~M35ParsingLog()
{
    delete ui;
}

void M35ParsingLog::init(QString project)
{
    project_     = project;
    xml_log_dir_ = QString("./projects/%1/xml_log").arg(project_);

    // 初始化后再插入日志类型,注意代码顺序不要反
    ui->WDT_Parse_Log->init(project_);
    //
    load_log_xml(xml_log_dir_);
}

void M35ParsingLog::load_log_xml(QString dir)
{
    QDir        directory(dir);
    QStringList filter("*.xml");
    QStringList file_list = directory.entryList(filter, QDir::Files);

    foreach (QString file, file_list) {
        QString path         = QString("%1/%2").arg(dir).arg(file);
        QString type         = xml_parser_.read_title_attribute(path, "LOG", "LOG_TYPE");
        QString size         = xml_parser_.read_title_attribute(path, "LOG", "LOG_RECORD_SIZE");
        QString default_item = xml_parser_.read_title_attribute(path, "LOG", "LOG_DEFAULT");
        QString start_offset = xml_parser_.read_title_attribute(path, "LOG", "START_OFFSET");
        QString max_rows     = xml_parser_.read_title_attribute(path, "LOG", "SHEET_MAX_RECORD");

        QVariantMap map;
        map.insert("xml_file", path);
        map.insert("group_text", "LOG");
        map.insert("default_item", default_item);

        if (size != "" && size != "-1") {
            map.insert("record_size", size.toInt());   // 单条日志长度,下位机定义
        }
        //
        if (start_offset != "" && start_offset != "-1") {
            map.insert("start_offset", start_offset.toInt());
        }
        else {
            map.insert("start_offset", 0);
        }
        //
        if (max_rows != "" && max_rows != "-1" && max_rows != "0") {
            map.insert("sheet_max_rows", max_rows.toInt());
        }
        else {
            map.insert("sheet_max_rows", 5000);
        }
        //
        ui->WDT_Parse_Log->insert_log_config(type, map);
    }
}

int M35ParsingLog::after_state_changed()
{
    IoEasyLog easylog(project_);
    easylog.log_info("当前模块: HAVC-M35/PHM-日期解析");
}
