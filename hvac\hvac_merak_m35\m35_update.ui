<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>M35Update</class>
 <widget class="QWidget" name="M35Update">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>905</width>
    <height>616</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">.QWidget
{
	font-size: 14px;
}

QPushButton
{
	border: 1px solid #7f8585; 
	border-radius: 3px; 	
	background-color: rgb(225, 225, 225);
	font-size: 14px;
	min-height: 28px;
	min-width: 120px;
}

QPushButton:hover 
{
	background-color: #4b6e8a; 
	color:#ffffff;
}

QGroupBox
{
	border:1px solid #7f8585;
	margin-top: 1.9ex;
}

QGroupBox::title 
{ 
	subcontrol-origin: margin;
	subcontrol-position: top left;
   	 left:10px;     
   	margin-left: 0px;
    	padding:0 1px;   
}

QProgressBar 
{
	background-color: #f0f0f0; 
	border-radius: 3px; 
	border: 1px solid #ccc; 
	height: 15px; 
	color: #333; 
	padding: 1px;
	min-height: 25px;
}

QProgressBar::chunk 
{
    background-color: #4CAF50; 
    border-radius: 3px; 
	color:white;
}</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" rowstretch="1,0">
   <property name="leftMargin">
    <number>2</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <property name="spacing">
    <number>2</number>
   </property>
   <item row="0" column="0">
    <widget class="IoFtpWidget" name="WDT_Ftp" native="true"/>
   </item>
   <item row="1" column="0">
    <widget class="QGroupBox" name="GB_Control">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="title">
      <string/>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <property name="leftMargin">
       <number>2</number>
      </property>
      <property name="topMargin">
       <number>2</number>
      </property>
      <property name="rightMargin">
       <number>2</number>
      </property>
      <property name="bottomMargin">
       <number>2</number>
      </property>
      <property name="spacing">
       <number>2</number>
      </property>
      <item row="3" column="1">
       <widget class="QProgressBar" name="PRB_To_K21">
        <property name="value">
         <number>0</number>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QProgressBar" name="PRB_To_K20">
        <property name="value">
         <number>0</number>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QProgressBar" name="PRB_To_K3K4">
        <property name="value">
         <number>0</number>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QProgressBar" name="PRB_To_K2">
        <property name="value">
         <number>0</number>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="LB_To_K2">
        <property name="minimumSize">
         <size>
          <width>110</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K1-&gt;K2/...</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="LB_To_K3K4">
        <property name="minimumSize">
         <size>
          <width>110</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K2-&gt;K3/K4</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="LB_To_K20">
        <property name="minimumSize">
         <size>
          <width>110</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K1-&gt;K20</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="LB_To_K21">
        <property name="minimumSize">
         <size>
          <width>110</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K20-&gt;K21</string>
        </property>
       </widget>
      </item>
      <item row="0" column="2" rowspan="4">
       <widget class="QPushButton" name="PB_Restart">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>122</width>
          <height>30</height>
         </size>
        </property>
        <property name="text">
         <string>复位控制器(仅K1)</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>IoFtpWidget</class>
   <extends>QWidget</extends>
   <header>../io_custom_component/io_ftp_widget/io_ftp_widget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
