﻿#ifndef M35_AI_ADJUST_H
#define M35_AI_ADJUST_H

#include "hvac_global.h"
#include <QWidget>
#include <QTranslator>

#include "../io_easy_log/io_easy_log.h"
#include "../io_datetime/io_datetime.h"
#include "../io_xml_parser/io_xml_parser.h"

#include "../io_base_controls/io_base_controls.h"
#include "../io_custom_component/io_base_form/io_base_form.h"

namespace Ui {
class M35AiAdjust;
}

class HVACSHARED_EXPORT M35AiAdjust : public IoBaseControls
{
    Q_OBJECT

public:
    explicit M35AiAdjust(QWidget *parent = 0);
    ~M35AiAdjust();

private:
    Ui::M35AiAdjust *ui;

public:
    // 通讯状态机
    enum COMMUNICATE_STATE{
        GET_AI,
        STOP
    };

    void init(QString project);
    void set_checked(bool state);
    //-------------------------------------------------
    void parse_data(unsigned char* dat,int len,void* buffer,int buffer_len) override;
    int pack_data(unsigned char* dat,uint8_t major_id,uint8_t minor_id) override;

    int after_state_changed() override;

private:
    void init_connect();

private:
    QString project_;
    QString ai_adjust_16_;
    QString ai_adjust_32_;

    //-------------------------------------------------
    IoXmlParser xml_parser_;
};

#endif // KTX_AI_AO_H
