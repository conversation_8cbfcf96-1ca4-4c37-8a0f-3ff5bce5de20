﻿#include "m35_device_info.h"
#include "ui_m35_device_info.h"

#include <QHostAddress>
#include <QtDebug>
#include <QtEndian>
#include <time.h>

M35DeviceInfo::M35DeviceInfo(QWidget* parent)
    : IoBaseControls(parent)
    , ui(new Ui::M35DeviceInfo)
{
    ui->setupUi(this);

    init_widget();
    init_connect();
}

M35DeviceInfo::~M35DeviceInfo()
{
    delete ui;
}

//-------------------------------------------------
// 通讯数据相关
void M35DeviceInfo::parse_data(unsigned char* dat, int len, void* buffer, int buffer_len)
{
    if (current_state_ == GET_DEVICE_INFO1) {
        if (dat[0] != 0xC0 || dat[1] != 01) {
            return;
        }
        parse_device_info1(dat, len);
        change_current_state(GET_DEVICE_INFO2, 1000, true);
    }
    else if (current_state_ == GET_DEVICE_INFO2) {
        if (dat[0] != 0xC0 || dat[1] != 02) {
            return;
        }
        parse_device_info2(dat, len);
        change_current_state(GET_DEVICE_INFO1, 1000, true);
    }
    else if (current_state_ == SET_K1_RTC) {
        device_info2_request_.k1_rtc_syn = 0;
        change_current_state(GET_DEVICE_INFO1, 1000, true);
        return;
    }
    else if (current_state_ == STOP) {
        change_current_state(STOP, 100, true);
        return;
    }
}

int M35DeviceInfo::pack_data(unsigned char* dat, uint8_t major_id, uint8_t minor_id)
{
    if (current_state_ == GET_DEVICE_INFO1) {
        uint16_t send_length = pack_device_info1(dat);
        change_current_state(1000, false);
        return send_length;
    }
    else if (current_state_ == GET_DEVICE_INFO2) {
        uint16_t send_length = pack_device_info2(dat);
        change_current_state(1000, false);
        return send_length;
    }
    else if (current_state_ == SET_K1_RTC) {
        uint16_t send_length = pack_set_k1_rtc(dat);
        change_current_state(1000, false);
        return send_length;
    }
    else if (current_state_ == STOP) {
        change_current_state(STOP, 100, true);
        return 0;
    }
}

int M35DeviceInfo::after_state_changed()
{
    change_current_state(GET_DEVICE_INFO1, 1000, true);

    //
    IoEasyLog easylog(project_);
    easylog.log_info("当前模块: HAVC-M35-通讯状态");
}

void M35DeviceInfo::init_widget()
{
    timer_.setTimerType(Qt::PreciseTimer);
    timer_.setInterval(1000);
}

//-------------------------------------------------
// 初始化相关
void M35DeviceInfo::init(QString project)
{
    project_ = project;
    change_current_state(GET_DEVICE_INFO1, 1000, true);
}

void M35DeviceInfo::init_connect()
{
    // 同步K1_RTC
    connect(ui->PB_K1_Rtc, &QPushButton::clicked, this, [=]() { change_next_state(SET_K1_RTC, 1000, true); });

    // 断开2秒后显示离线
    connect(&timer_, &QTimer::timeout, this, [=]() {
        QList<QWidget*> module = findChildren<QWidget*>();
        for (QWidget* child : module) {
            if (child->dynamicPropertyNames().contains("online")) {
                child->setProperty("online", 2);
                child->style()->polish(child);
            }
        }
        timer_.stop();
    });
}

// 网络状态 请求
int M35DeviceInfo::pack_device_info1(unsigned char* dat)
{
    uint16_t send_length = sizeof(DEVICE_INFO1_REQUEST);
    memset(&device_info1_request_, 0, sizeof(DEVICE_INFO1_REQUEST));

    device_info1_request_.head    = 0x80;
    device_info1_request_.command = 0x01;
    device_info1_request_.size    = qToBigEndian(send_length);   // 大端
    //
    memcpy(dat, &device_info1_request_, send_length);
    return send_length;
}

// 设备信息 请求
int M35DeviceInfo::pack_device_info2(unsigned char* dat)
{
    uint16_t send_length = sizeof(DEVICE_INFO2_REQUEST);
    memset(&device_info2_request_, 0, sizeof(DEVICE_INFO2_REQUEST));
    //
    device_info2_request_.head      = 0x80;
    device_info2_request_.command   = 0x02;
    device_info2_request_.size      = qToBigEndian(send_length);   // 大端
    device_info2_request_.local_rtc = time(nullptr);
    //
    memcpy(dat, &device_info2_request_, send_length);
    return send_length;
}

//
int M35DeviceInfo::pack_set_k1_rtc(unsigned char* dat)
{
    uint16_t send_length = sizeof(DEVICE_INFO2_REQUEST);
    memset(&device_info2_request_, 0, sizeof(DEVICE_INFO2_REQUEST));
    //
    device_info2_request_.head    = 0x80;
    device_info2_request_.command = 0x02;
    device_info2_request_.size    = qToBigEndian(send_length);   // 大端
    // 同步RTC
    device_info2_request_.local_rtc  = time(nullptr);
    device_info2_request_.k1_rtc_syn = 1;
    //
    memcpy(dat, &device_info2_request_, send_length);

    // 记录操作日志
    IoEasyLog easylog(project_);
    QString   log_rtc = QString(tr("同步RTC:%1")).arg(IoDateTime::get_datetime_string2());
    easylog.log_info(log_rtc);

    return send_length;
}

// 网络状态 回复
void M35DeviceInfo::parse_device_info1(unsigned char* dat, int len)
{
    memcpy(&device_info1_response_, dat, sizeof(DEVICE_INFO1_RESPONSE));
    //
    parse_k1_state();
    parse_k2_state();
    parse_k3_state();
    parse_k4_state();
    parse_k20_state();
    parse_k21_state();
    //
    parse_k1_soft();
    parse_k2_soft();
    parse_k3_soft();
    parse_k4_soft();
    parse_k20_soft();
    parse_k21_soft();
    //
    parse_k1_app();
    parse_k20_app();
    //
    parse_cbm02_soft();
    parse_mpb_soft();
    //
    polish_qss();
}

// 设备信息 回复
void M35DeviceInfo::parse_device_info2(unsigned char* dat, int len)
{
    memcpy(&device_info2_response_, dat, sizeof(DEVICE_INFO2_RESPONSE));
    //
    parse_k1_rtc();
    parse_k1_boot();
    parse_k2_boot();
    parse_k3_boot();
    parse_k4_boot();
    parse_k20_boot();
    parse_k21_boot();
    //
    parse_mvb_soft();
}

void M35DeviceInfo::parse_k1_rtc()
{
    QString k1_rtc = "";
    k1_rtc         = QDateTime::fromTime_t(device_info2_response_.k1_rtc).toLocalTime().toString("yyyy-MM-dd hh:mm:ss");
    ui->LB_K1_Rtc->setText(k1_rtc);
}

void M35DeviceInfo::parse_k1_state()
{
    ui->LB_K1_485_0->setProperty("online", device_info1_response_.k1_485_0);
    ui->LB_K1_485_1->setProperty("online", device_info1_response_.k1_485_1);
    ui->LB_K1_232->setProperty("online", device_info1_response_.k1_232);
    ui->LB_K1_Can2->setProperty("online", device_info1_response_.k1_can2);
    ui->LB_K1_Eth->setProperty("online", device_info1_response_.k1_eth);
    ui->LB_K1_Mvb->setProperty("online", device_info1_response_.k1_ttl);

    ui->LINE_K1_485_0->setProperty("online", device_info1_response_.k1_485_0);
    ui->LINE_K1_485_1->setProperty("online", device_info1_response_.k1_485_1);
    ui->LINE_K1_232_1->setProperty("online", device_info1_response_.k1_232);
    ui->LINE_K1_232_2->setProperty("online", device_info1_response_.k1_232);
}

void M35DeviceInfo::parse_k2_state()
{
    ui->LB_K2_485_0->setProperty("online", device_info1_response_.k2_485_0);
    ui->LB_K2_485_1->setProperty("online", device_info1_response_.k2_485_1);
    ui->LB_K2_Can->setProperty("online", device_info1_response_.k2_can);

    ui->LINE_K2_485_0->setProperty("online", device_info1_response_.k2_485_0);
    ui->LINE_K2_485_1->setProperty("online", device_info1_response_.k2_485_1);
}

void M35DeviceInfo::parse_k3_state()
{
    ui->LB_K3_Can->setProperty("online", device_info1_response_.k3_can);
    ui->LINE_K3_Can->setProperty("online", device_info1_response_.k3_can);
}

void M35DeviceInfo::parse_k4_state()
{
    ui->LB_K4_Can->setProperty("online", device_info1_response_.k4_can);
    ui->LINE_K4_Can->setProperty("online", device_info1_response_.k4_can);
}

void M35DeviceInfo::parse_k20_state()
{
    ui->LB_K20_485_0->setProperty("online", device_info1_response_.k20_485_0);
    ui->LB_K20_485_1->setProperty("online", device_info1_response_.k20_485_1);
    ui->LB_K20_Can->setProperty("online", device_info1_response_.k20_can);

    ui->LINE_K20_Can->setProperty("online", device_info1_response_.k20_can);
    ui->LINE_K20_485_0->setProperty("online", device_info1_response_.k20_485_0);
    ui->LINE_K20_485_1->setProperty("online", device_info1_response_.k20_485_1);
}

void M35DeviceInfo::parse_k21_state()
{
    ui->LB_K21_Can->setProperty("online", device_info1_response_.k21_can);
    ui->LINE_K21_Can->setProperty("online", device_info1_response_.k21_can);
}

void M35DeviceInfo::parse_k1_soft()
{
    QString k1_fw;
    k1_fw.sprintf("%X.%02X", (device_info1_response_.k1_soft_ver >> 8) & 0x0F, device_info1_response_.k1_soft_ver & 0xFF);
    ui->LB_K1_Fw->setText(k1_fw);
}

void M35DeviceInfo::parse_k2_soft()
{
    QString k2_fw;
    k2_fw.sprintf("%X.%02X", (device_info1_response_.k2_soft_ver >> 8) & 0x0F, device_info1_response_.k2_soft_ver & 0xFF);
    ui->LB_K2_Fw->setText(k2_fw);
}

void M35DeviceInfo::parse_k3_soft()
{
    QString k3_fw;
    k3_fw.sprintf("%X.%02X", (device_info1_response_.k3_soft_ver >> 8) & 0x0F, device_info1_response_.k3_soft_ver & 0xFF);
    ui->LB_K3_Fw->setText(k3_fw);
}

void M35DeviceInfo::parse_k4_soft()
{
    QString k4_fw;
    k4_fw.sprintf("%X.%02X", (device_info1_response_.k4_soft_ver >> 8) & 0x0F, device_info1_response_.k4_soft_ver & 0xFF);
    ui->LB_K4_Fw->setText(k4_fw);
}

void M35DeviceInfo::parse_k20_soft()
{
    QString k20_fw;
    k20_fw.sprintf("%X.%02X", (device_info1_response_.k20_soft_ver >> 8) & 0x0F, device_info1_response_.k20_soft_ver & 0xFF);
    ui->LB_K20_Fw->setText(k20_fw);
}

void M35DeviceInfo::parse_k21_soft()
{
    QString k21_fw;
    k21_fw.sprintf("%X.%02X", (device_info1_response_.k21_soft_ver >> 8) & 0x0F, device_info1_response_.k21_soft_ver & 0xFF);
    ui->LB_K21_Fw->setText(k21_fw);
}

void M35DeviceInfo::parse_k1_app()
{
    QString k1_app;
    k1_app.sprintf("%X.%02X", (device_info1_response_.k1_app_ver >> 8) & 0x0F, device_info1_response_.k1_app_ver & 0xFF);
    ui->LB_K1_App->setText(k1_app);
}

void M35DeviceInfo::parse_k20_app()
{
    QString k20_app;
    k20_app.sprintf("%X.%02X", (device_info1_response_.k20_app_ver >> 8) & 0x0F, device_info1_response_.k20_app_ver & 0xFF);
    ui->LB_K20_App->setText(k20_app);
}

// MVB,CBM02,MPB 版本号与K1,K2等格式保持一致,与项工协商好的 -> 2025-3-17 10:07:25
void M35DeviceInfo::parse_mvb_soft()
{
    QString mvb_soft;
    mvb_soft.sprintf("%X.%02X", (device_info2_response_.mvb_soft_ver >> 8) & 0x0F, device_info2_response_.mvb_soft_ver & 0xFF);
    ui->LB_Mvb_Soft->setText(mvb_soft);
}

void M35DeviceInfo::parse_cbm02_soft()
{
    QString cbm02_soft;
    cbm02_soft.sprintf("%X.%02X", (device_info1_response_.cbm_soft_ver >> 8) & 0x0F, device_info1_response_.cbm_soft_ver & 0xFF);
    ui->LB_Cbm02_Ver->setText(cbm02_soft);
}

void M35DeviceInfo::parse_mpb_soft()
{
    QString mpb_soft;
    mpb_soft.sprintf("%X.%02X", (device_info1_response_.mpb_soft_ver >> 8) & 0x0F, device_info1_response_.mpb_soft_ver & 0xFF);
    ui->LB_MPB_Soft->setText(mpb_soft);
}

void M35DeviceInfo::parse_k1_boot()
{
    QString k1_boot;
    k1_boot.sprintf("%02X.%02X", device_info2_response_.k1_boot_ver >> 8, device_info2_response_.k1_boot_ver & 0xFF);
    ui->LB_K1_Boot->setText(k1_boot);
}

void M35DeviceInfo::parse_k2_boot()
{
    QString k2_boot;
    k2_boot.sprintf("%02X.%02X", device_info2_response_.k2_boot_ver >> 8, device_info2_response_.k2_boot_ver & 0xFF);
    ui->LB_K2_Boot->setText(k2_boot);
}

void M35DeviceInfo::parse_k3_boot()
{
    QString k3_boot;
    k3_boot.sprintf("%02X.%02X", device_info2_response_.k3_boot_ver >> 8, device_info2_response_.k3_boot_ver & 0xFF);
    ui->LB_K3_Boot->setText(k3_boot);
}

void M35DeviceInfo::parse_k4_boot()
{
    QString k4_boot;
    k4_boot.sprintf("%02X.%02X", device_info2_response_.k4_boot_ver >> 8, device_info2_response_.k4_boot_ver & 0xFF);
    ui->LB_K4_Boot->setText(k4_boot);
}

void M35DeviceInfo::parse_k20_boot()
{
    QString k20_boot;
    k20_boot.sprintf("%02X.%02X", device_info2_response_.k20_boot_ver >> 8, device_info2_response_.k20_boot_ver & 0xFF);
    ui->LB_K20_Boot->setText(k20_boot);
}

void M35DeviceInfo::parse_k21_boot()
{
    QString k21_boot;
    k21_boot.sprintf("%02X.%02X", device_info2_response_.k21_boot_ver >> 8, device_info2_response_.k21_boot_ver & 0xFF);
    ui->LB_K21_Boot->setText(k21_boot);
}

void M35DeviceInfo::polish_qss()
{
    QList<QWidget*> childs = findChildren<QWidget*>();
    for (QWidget* child : childs) {
        if (child->dynamicPropertyNames().contains("online")) {
            child->style()->polish(child);
        }
    }
    // 实时检测断线
    timer_.start();
}
