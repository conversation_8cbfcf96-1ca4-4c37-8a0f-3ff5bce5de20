﻿#ifndef M35_DI_DO_H
#define M35_DI_DO_H

#include "hvac_global.h"
#include <QWidget>
#include <QTranslator>

#include "../io_easy_log/io_easy_log.h"
#include "../io_datetime/io_datetime.h"
#include "../io_xml_parser/io_xml_parser.h"
#include "../io_base_controls/io_base_controls.h"
#include "../io_custom_component/io_base_form/io_base_form.h"

namespace Ui {
class M35DiDo;
}

class HVACSHARED_EXPORT M35DiDo : public IoBaseControls
{
    Q_OBJECT

public:
    explicit M35DiDo(QWidget *parent = 0);
    ~M35DiDo();

private:
    Ui::M35DiDo *ui;

public:
    // 通讯状态机
    enum COMMUNICATE_STATE{
        GET_DI_DO,
        STOP
    };

    void init(QString project);
    //-------------------------------------------------
    void parse_data(unsigned char* dat,int len,void* buffer,int buffer_len) override;
    int pack_data(unsigned char* dat,uint8_t major_id,uint8_t minor_id) override;
    //-------------------------------------------------
    int after_state_changed() override;

private:
    QString project_;
    //
    QString dio_control_xml_;
    QString dio_driver_xml_;
    QString dio_extend_xml_;
    QString dio_bgd_xml_;
    QString dio_exio_xml_;
    //-------------------------------------------------
    IoXmlParser xml_parser_;
};

#endif // KTX_DI_DO_H
