<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>M35DeviceInfo</class>
 <widget class="QWidget" name="M35DeviceInfo">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1357</width>
    <height>761</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">/*以下QSS为了跟原代码保持一致*/
#M35DeviceInfo{
	background-color: rgb(255, 255, 255);
}

QLabel{
	font-family: &quot;宋体&quot;; 
	font-size: 14px; 
}

QLineEdit{
	font-size: 14px; 
	min-height: 25px;
}

QPushButton{
	border: 1px solid #7f8585; 
	border-radius: 3px; 	
	font-size: 14px;
	min-height: 28px;
	background-color: rgb(215, 215, 215)
}

QPushButton:hover {
	background-color: #4b6e8a; 
	color:#ffffff;
}

QGroupBox{
	border:2px solid #7f8585;
	font-size:14px;
	border-radius: 3px; 
	margin-top: 9px;    
}

QGroupBox::title{
	subcontrol-origin: margin;
	subcontrol-position: top center;
	padding: 0 3px;
	font-weight: bold; 
	font-size: 14px; 
}

#WDT_Ver,#WDT_Top{
	border: 1px solid #7f8585; 
}

#GB_Expand,#GB_Main,#GB_Driver{
	border: 2px dotted;
}

#GB_K1,#GB_K2,#GB_K3,#GB_K4,#GB_K20,#GB_K21,#GB_LED{
	background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,stop: 0 #E0E0E0, stop: 1 #FFFFFF);
	border-radius: 5px;
	font-size:14px;
	margin-top: 9px;    
}

#WDT_Ver2 &gt; QLabel, #WDT_Ver &gt; QLabel,#LB_K1_Rtc,#LB_K1_Rtc_Lab{
	font-family: &quot;Segoe UI Semibold&quot;; 
	font-size: 18px; 
}

#LINE_Can_1,#LINE_Can_2{
	border: 1px solid #548B54;
	background:#548B54;
}
#LINE_485_1{
	border: 1px solid #548B54;
	background:#5F9EA0;
}



/*以下QSS做网络状态显示使用*/
QFrame[online=&quot;2&quot;]{	/*  初始化  */
	border: 1px solid #7f8585;
	background:#7f8585;
}
QFrame[online=&quot;1&quot;]{	/*  在线  */
	border: 1px solid #548B54;
	background:#548B54;
}
QFrame[online=&quot;0&quot;]{	/*  离线  */
	border: 1px solid #FF3030;
	background:#FF3030;
}

QLabel[online=&quot;2&quot;]{
	border: 2px solid #7f8585; 
	background: transparent;
	font-family: &quot;宋体&quot;; 
	font-size: 14px;       
	font: bold 14px;      
}
QLabel[online=&quot;1&quot;]{
	border: 2px solid #548B54; 
	background: transparent;
	font-family: &quot;宋体&quot;; 
	font-size: 14px;       
	font: bold 14px;             
}
QLabel[online=&quot;0&quot;]{
	border: 2px solid #FF3030; 
	background: transparent;
	font-family: &quot;宋体&quot;; 
	font-size: 14px;       
	font: bold 14px;    
}
</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_4" rowstretch="8,1">
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>5</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <property name="spacing">
    <number>5</number>
   </property>
   <item row="0" column="0">
    <widget class="QWidget" name="WDT_Top" native="true">
     <layout class="QGridLayout" name="gridLayout_6" columnstretch="0,0,0">
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="topMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>5</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <property name="spacing">
       <number>5</number>
      </property>
      <item row="0" column="1">
       <widget class="QWidget" name="WDT_Topo" native="true">
        <property name="minimumSize">
         <size>
          <width>1320</width>
          <height>600</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>1320</width>
          <height>600</height>
         </size>
        </property>
        <widget class="QGroupBox" name="GB_Expand">
         <property name="geometry">
          <rect>
           <x>30</x>
           <y>233</y>
           <width>210</width>
           <height>221</height>
          </rect>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>Segoe UI Semibold</family>
           <pointsize>-1</pointsize>
           <weight>50</weight>
           <italic>false</italic>
           <bold>false</bold>
          </font>
         </property>
         <property name="title">
          <string>扩展板</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignHCenter|Qt::AlignTop</set>
         </property>
         <layout class="QGridLayout" name="gridLayout_10">
          <item row="0" column="0">
           <widget class="QGroupBox" name="GB_K4">
            <property name="title">
             <string>K4</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <layout class="QGridLayout" name="gridLayout_5" rowstretch="1,1">
             <item row="1" column="0">
              <spacer name="verticalSpacer">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>68</height>
                </size>
               </property>
              </spacer>
             </item>
             <item row="0" column="0">
              <widget class="QLabel" name="LB_K4_Can">
               <property name="text">
                <string>CAN1</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QGroupBox" name="GB_Main">
         <property name="geometry">
          <rect>
           <x>250</x>
           <y>233</y>
           <width>600</width>
           <height>221</height>
          </rect>
         </property>
         <property name="font">
          <font>
           <family>Segoe UI Semibold</family>
           <pointsize>-1</pointsize>
           <weight>50</weight>
           <italic>false</italic>
           <bold>false</bold>
          </font>
         </property>
         <property name="title">
          <string>主控板</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignHCenter|Qt::AlignTop</set>
         </property>
         <layout class="QGridLayout" name="gridLayout_12">
          <property name="spacing">
           <number>7</number>
          </property>
          <item row="0" column="0">
           <widget class="QGroupBox" name="GB_K3">
            <property name="title">
             <string>K3</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <layout class="QGridLayout" name="gridLayout_13" rowstretch="1,1">
             <item row="1" column="0">
              <spacer name="verticalSpacer_2">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
             <item row="0" column="0">
              <widget class="QLabel" name="LB_K3_Can">
               <property name="text">
                <string>CAN1</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QGroupBox" name="GB_K1">
            <property name="title">
             <string>K1</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <layout class="QGridLayout" name="gridLayout_14">
             <item row="2" column="0">
              <widget class="QLabel" name="LB_K1_485_1">
               <property name="text">
                <string>RS485-1</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
             <item row="0" column="0" rowspan="2">
              <widget class="QLabel" name="LB_K1_Can2">
               <property name="text">
                <string>CAN2</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="LB_K1_485_0">
               <property name="text">
                <string>RS485-0</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
             <item row="2" column="2">
              <widget class="QLabel" name="LB_K1_232">
               <property name="text">
                <string>IOIOI 1
(232)</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
             <item row="0" column="1" colspan="2">
              <widget class="QGroupBox" name="GB_ETH">
               <property name="title">
                <string>网卡板</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <layout class="QGridLayout" name="gridLayout_15">
                <item row="0" column="0">
                 <widget class="QLabel" name="LB_K1_Mvb">
                  <property name="text">
                   <string>MVB</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="online" stdset="0">
                   <number>2</number>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="1" column="1" colspan="2">
              <widget class="QLabel" name="LB_K1_Eth">
               <property name="text">
                <string>ETH</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QGroupBox" name="GB_K2">
            <property name="title">
             <string>K2</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <layout class="QGridLayout" name="gridLayout_17">
             <item row="0" column="0" colspan="2">
              <widget class="QLabel" name="LB_K2_Can">
               <property name="text">
                <string>CAN(内部)</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="LB_K2_485_0">
               <property name="text">
                <string>RS485-0</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="LB_K2_485_1">
               <property name="text">
                <string>RS485-1</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QGroupBox" name="GB_Driver">
         <property name="geometry">
          <rect>
           <x>911</x>
           <y>233</y>
           <width>400</width>
           <height>221</height>
          </rect>
         </property>
         <property name="font">
          <font>
           <family>Segoe UI Semibold</family>
           <pointsize>-1</pointsize>
           <weight>50</weight>
           <italic>false</italic>
           <bold>false</bold>
          </font>
         </property>
         <property name="title">
          <string>司机室板</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignHCenter|Qt::AlignTop</set>
         </property>
         <layout class="QGridLayout" name="gridLayout_18">
          <item row="0" column="0" rowspan="2" colspan="2">
           <widget class="QGroupBox" name="GB_K20">
            <property name="title">
             <string>K20</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <layout class="QGridLayout" name="gridLayout_22">
             <item row="1" column="0">
              <widget class="QLabel" name="LB_K20_485_1">
               <property name="text">
                <string>RS485-1</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="LB_K20_485_0">
               <property name="text">
                <string>RS485-0</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
             <item row="0" column="0" colspan="2">
              <widget class="QLabel" name="LB_K20_Can">
               <property name="text">
                <string>CAN</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="0" column="2" rowspan="2">
           <widget class="QGroupBox" name="GB_K21">
            <property name="title">
             <string>K21</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <layout class="QGridLayout" name="gridLayout_23" rowstretch="1,1">
             <item row="0" column="0">
              <widget class="QLabel" name="LB_K21_Can">
               <property name="text">
                <string>CAN1</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="online" stdset="0">
                <number>2</number>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <spacer name="verticalSpacer_3">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QLabel" name="LB_Cab_Bus_1">
         <property name="geometry">
          <rect>
           <x>20</x>
           <y>124</y>
           <width>72</width>
           <height>15</height>
          </rect>
         </property>
         <property name="text">
          <string>CAN-BUS</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
        <widget class="Line" name="LINE_K3_Can">
         <property name="geometry">
          <rect>
           <x>340</x>
           <y>140</y>
           <width>7</width>
           <height>100</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_Can_1">
         <property name="geometry">
          <rect>
           <x>90</x>
           <y>124</y>
           <width>551</width>
           <height>16</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
        <widget class="Line" name="LINE_K4_Can">
         <property name="geometry">
          <rect>
           <x>140</x>
           <y>140</y>
           <width>7</width>
           <height>95</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_K1_232_2">
         <property name="geometry">
          <rect>
           <x>880</x>
           <y>215</y>
           <width>7</width>
           <height>190</height>
          </rect>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_K1_232_1">
         <property name="geometry">
          <rect>
           <x>848</x>
           <y>398</y>
           <width>38</width>
           <height>7</height>
          </rect>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="QGroupBox" name="GB_LED">
         <property name="geometry">
          <rect>
           <x>840</x>
           <y>135</y>
           <width>91</width>
           <height>81</height>
          </rect>
         </property>
         <property name="font">
          <font>
           <family>Segoe UI Semibold</family>
           <pointsize>-1</pointsize>
           <weight>50</weight>
           <italic>false</italic>
           <bold>false</bold>
          </font>
         </property>
         <property name="title">
          <string>触摸屏</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignHCenter|Qt::AlignTop</set>
         </property>
         <layout class="QGridLayout" name="gridLayout_11">
          <item row="0" column="0">
           <widget class="QLabel" name="EX_LED_2">
            <property name="text">
             <string>LED</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="Line" name="LINE_K2_485_0">
         <property name="geometry">
          <rect>
           <x>567</x>
           <y>430</y>
           <width>7</width>
           <height>88</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_K20_485_1">
         <property name="geometry">
          <rect>
           <x>986</x>
           <y>430</y>
           <width>7</width>
           <height>88</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_K1_485_1">
         <property name="geometry">
          <rect>
           <x>664</x>
           <y>430</y>
           <width>7</width>
           <height>88</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="QLabel" name="LB_485_1">
         <property name="geometry">
          <rect>
           <x>1102</x>
           <y>509</y>
           <width>91</width>
           <height>16</height>
          </rect>
         </property>
         <property name="font">
          <font>
           <family>宋体</family>
           <pointsize>-1</pointsize>
           <weight>50</weight>
           <italic>false</italic>
           <bold>false</bold>
          </font>
         </property>
         <property name="text">
          <string>RS485-BUS</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
        <widget class="Line" name="LINE_485_1">
         <property name="geometry">
          <rect>
           <x>526</x>
           <y>511</y>
           <width>570</width>
           <height>16</height>
          </rect>
         </property>
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
        <widget class="Line" name="LINE_K1_485_0">
         <property name="geometry">
          <rect>
           <x>730</x>
           <y>430</y>
           <width>7</width>
           <height>13</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_K2_485_1">
         <property name="geometry">
          <rect>
           <x>490</x>
           <y>430</y>
           <width>7</width>
           <height>13</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_K20_485_0">
         <property name="geometry">
          <rect>
           <x>1100</x>
           <y>430</y>
           <width>7</width>
           <height>13</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_K20_Can">
         <property name="geometry">
          <rect>
           <x>1030</x>
           <y>142</y>
           <width>7</width>
           <height>100</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_K21_Can">
         <property name="geometry">
          <rect>
           <x>1220</x>
           <y>142</y>
           <width>7</width>
           <height>100</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="online" stdset="0">
          <number>2</number>
         </property>
        </widget>
        <widget class="Line" name="LINE_Can_2">
         <property name="geometry">
          <rect>
           <x>980</x>
           <y>126</y>
           <width>271</width>
           <height>16</height>
          </rect>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
        <widget class="QLabel" name="LB_Can_Bus_2">
         <property name="geometry">
          <rect>
           <x>1250</x>
           <y>126</y>
           <width>72</width>
           <height>15</height>
          </rect>
         </property>
         <property name="font">
          <font>
           <family>宋体</family>
           <pointsize>-1</pointsize>
           <weight>50</weight>
           <italic>false</italic>
           <bold>false</bold>
          </font>
         </property>
         <property name="text">
          <string>CAN-BUS</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
        <widget class="QWidget" name="WDT_Ver2" native="true">
         <property name="geometry">
          <rect>
           <x>51</x>
           <y>460</y>
           <width>431</width>
           <height>91</height>
          </rect>
         </property>
         <layout class="QGridLayout" name="gridLayout_2">
          <item row="0" column="0">
           <widget class="QLabel" name="LB_K1_App_Lab">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>K1校验值:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_K1_App">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="LB_Cbm02_Ver_Lab">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>CBM02版本号:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QLabel" name="LB_Cbm02_Ver">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="LB_K20_App_Lab">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>K20校验值:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="LB_K20_App">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="LB_MPB_Soft_Lab">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>MPB版本号:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QLabel" name="LB_MPB_Soft">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item row="2" column="2">
           <widget class="QLabel" name="LB_Mvb_Soft_Lab">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>MVB版本号:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="2" column="3">
           <widget class="QLabel" name="LB_Mvb_Soft">
            <property name="minimumSize">
             <size>
              <width>50</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="WDT_Rtc" native="true">
         <property name="geometry">
          <rect>
           <x>870</x>
           <y>20</y>
           <width>381</width>
           <height>41</height>
          </rect>
         </property>
         <layout class="QGridLayout" name="gridLayout_3" columnstretch="1,2,1">
          <property name="leftMargin">
           <number>5</number>
          </property>
          <property name="topMargin">
           <number>5</number>
          </property>
          <property name="rightMargin">
           <number>5</number>
          </property>
          <property name="bottomMargin">
           <number>5</number>
          </property>
          <property name="spacing">
           <number>5</number>
          </property>
          <item row="0" column="2">
           <widget class="QPushButton" name="PB_K1_Rtc">
            <property name="text">
             <string>同步时钟</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="LB_K1_Rtc">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>2024-3-5 16:41:41</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="LB_K1_Rtc_Lab">
            <property name="text">
             <string>时钟:</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
      <item row="0" column="0">
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="2">
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QWidget" name="WDT_Ver" native="true">
     <layout class="QGridLayout" name="gridLayout">
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="topMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>5</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <property name="spacing">
       <number>5</number>
      </property>
      <item row="0" column="6">
       <widget class="QLabel" name="LB_K4_Fw_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K4 Firmware：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="8">
       <widget class="QLabel" name="LB_K20_Fw_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K20 Firmware：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="7">
       <widget class="QLabel" name="LB_K4_Fw">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="LB_K1_Boot_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K1 Bootloader：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="9">
       <widget class="QLabel" name="LB_K20_Fw">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="0" column="10">
       <widget class="QLabel" name="LB_K21_Fw_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K21 Firmware：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="11">
       <widget class="QLabel" name="LB_K21_Fw">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLabel" name="LB_K1_Boot">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="QLabel" name="LB_K2_Boot">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QLabel" name="LB_K2_Boot_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K2 Botloader：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="5">
       <widget class="QLabel" name="LB_K3_Boot">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="4">
       <widget class="QLabel" name="LB_K3_Boot_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K3 Bootloader：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="6">
       <widget class="QLabel" name="LB_K4_Boot_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K4 Bootloader：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="8">
       <widget class="QLabel" name="LB_K20_Boot_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K20 Bootloader：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="9">
       <widget class="QLabel" name="LB_K20_Boot">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="7">
       <widget class="QLabel" name="LB_K4_Boot">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="11">
       <widget class="QLabel" name="LB_K21_Boot">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="1" column="10">
       <widget class="QLabel" name="LB_K21_Boot_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K21 Bootloader：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLabel" name="LB_K1_Fw">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="LB_K2_Fw_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K2 Firmware：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="LB_K1_Fw_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K1 Firmware：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QLabel" name="LB_K2_Fw">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item row="0" column="4">
       <widget class="QLabel" name="LB_K3_Fw_Lab">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>K3 Firmware：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="5">
       <widget class="QLabel" name="LB_K3_Fw">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
